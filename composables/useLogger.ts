export const useLogger = () => {
  const config = useRuntimeConfig()

  const log = (...args: any[]) => {
    if (config.public.ENABLED_LOGS) {
      // eslint-disable-next-line no-console
      console.log(...args)
    }
  }

  const warn = (...args: any[]) => {
    if (config.public.ENABLED_LOGS) {
      // eslint-disable-next-line no-console
      console.warn(...args)
    }
  }

  const error = (...args: any[]) => {
    if (config.public.ENABLED_LOGS) {
      // eslint-disable-next-line no-console
      console.error(...args)
    }
  }

  return {
    log,
    warn,
    error,
  }
}