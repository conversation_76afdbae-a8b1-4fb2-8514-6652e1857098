export const useCreateProject = () => {
	const { whiteLabelApp } = storeToRefs(useMarketplacePublisher());
	const { isDeveloperWorkspace } = storeToRefs(useWorkspaceStore());

	const openAddProjectModal = () => {
		useDialogStore().switchDialog('createProjectController', true);
		useDialogStore().switchDialog('createProjectFromScratch', true);
	};

	const openTemplatesModal = () => {
		useWorkspaceStore().setProjectTemplateCards(isDeveloperWorkspace.value);
	};

	const openCreateProjectModal = () => {
		if (whiteLabelApp.value?.enableTemplateProject === false) {
			openAddProjectModal();
		} else {
			openTemplatesModal();
		}
	};

	return {
		openCreateProjectModal,
	};
};