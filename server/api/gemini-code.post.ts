/* eslint-disable no-console */
import { GoogleGenerativeAI } from '@google/generative-ai';
import axios from 'axios';
import { GLOBALS } from '~/helpers/contants/global_constants';

export default defineEventHandler(async (event) => {
	async function getUserGeminiApiKey(apiKeyPayload: any) {
		const runtimeBase =
			useRuntimeConfig().public.API_BASE_URL || 'https://api0.mitraecp.com:1005';
		const baseUrl: string =
			apiKeyPayload.baseUrl || apiKeyPayload.nuxtBaseUrl || runtimeBase;
		const payloadUserToken: string =
			apiKeyPayload.nuxtToken ??
			apiKeyPayload.authorizationToken ??
			apiKeyPayload.spaceToken ??
			'';
		if (!payloadUserToken) {
			throw createError({
				statusCode: 401,
				statusMessage: 'Missing authorization token'
			});
		}
		const isToUseUserIaCredit: boolean = apiKeyPayload.isToUseUserIaCredit ?? true;
		const isToUseOwnKey: boolean = apiKeyPayload.isToUseOwnKey ?? false;
		const selectedWorkspaceId: number =
			apiKeyPayload.selectedWorkspaceId ?? apiKeyPayload.workspaceId ?? 0;
		const freeIaCreditsUsage: number =
			apiKeyPayload.configChain?.freeIaCreditsUsage ??
			apiKeyPayload.freeIaCreditsUsage ??
			0;
		const isSankhya: boolean = apiKeyPayload.isSankhya ?? false;
		// console.log('baseUrl:', baseUrl);
		// console.log('payloadUserToken:', payloadUserToken);
		// console.log('isToUseUserIaCredit:', isToUseUserIaCredit);
		// console.log('isToUseOwnKey:', isToUseOwnKey);
		// console.log('selectedWorkspaceId:', selectedWorkspaceId);
		// console.log('freeIaCreditsUsage:', freeIaCreditsUsage);
		// console.log('isSankhya:', isSankhya);
		// Obter API key correta
		const apiKey = await getGeminiApiKey(
			baseUrl,
			payloadUserToken,
			isToUseUserIaCredit,
			isToUseOwnKey,
			selectedWorkspaceId,
			freeIaCreditsUsage,
			isSankhya
		);
		return apiKey;
	}
	async function getChainFromMitraAPI(chainEndpoint: string): Promise<string> {
		// BASEURL e Authorization hardcoded de um projeto existente no Mitra em https://app.mitralab.io/w/9300/p/14458/
		const mitraAxios = axios.create({
			baseURL: 'https://prod2.mitrasheet.com:8080/rest/v0',
			headers: {
				'Content-Type': 'application/json',
				Authorization: GLOBALS.API_CHAIN_TK
			}
		});

		// eslint-disable-next-line no-console
		console.log(`🔗 CHAIN DEBUG - Buscando CHAIN do endpoint: ${chainEndpoint}`);

		try {
			const response = await mitraAxios.get(`/${chainEndpoint}`);
			const chain = response.data;
			return chain?.content?.[0]?.[chainEndpoint.replaceAll(' ', '_')] || '';
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error('❌ CHAIN DEBUG - Erro ao buscar CHAIN:', error);
			return '';
		}
	}
	async function getCustomComponents() {
		try {
			const response = await axios.get(
				'https://prod2.mitrasheet.com:8080/rest/v0/Custom Components',
				{
					headers: {
						Authorization:
							'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDaGFpbiBBdHVhbCIsIlgtVGVuYW50SUQiOiJ0ZW5hbnRfMjU5ODYifQ.I6kI6lVdTm3YV7bRQsR9cNiMXr79SHftlzdvDZRpSaoZD-WnOwHdOI8d0m5g_vzBHsdvvBu0FuT2izwkVudvog'
					}
				}
			);
			return response.data;
		} catch (error) {
			console.error('❌ Erro ao buscar Custom Components:', error);
			throw error;
		}
	}
	function replaceCustomComponents(
		chainReplaced: string,
		customComponents: any
	): string {
		// Depois, tratar os componentes individuais normalmente
		customComponents?.content?.forEach((component: any) => {
			const description = component['Descrição'];

			if (description) {
				// Criar regex para encontrar todos os padrões $DESCRIPTION.PROPERTY
				const descriptionRegex = new RegExp(`\\$${description}\\.(\\w+)`, 'g');

				// Encontrar todas as ocorrências e fazer replace
				chainReplaced = chainReplaced.replace(
					descriptionRegex,
					(match: string, property: string) => {
						// Verificar se a propriedade existe no componente
						if (
							property in component &&
							component[property] !== undefined &&
							component[property] !== null
						) {
							// console.log(`🔄 Substituindo ${match} por:`, component[property]);
							return component[property];
						}

						// Se não encontrar a propriedade, manter o texto original
						console.warn(`⚠️ Propriedade '${property}' não encontrada em ${description}`);
						return match;
					}
				);
			}
		});
		return chainReplaced;
	}
	// Helper para gerar conteúdo via OpenRouter (Anthropic Claude) sem streaming
	async function generateContentOpenRouter({
		userPrompt,
		componentPrompt,
		files,
		chain,
		openRouterModel,
		apiKey,
		temperature
	}: {
		userPrompt: { text: string; ref: number }[];
		componentPrompt: Record<string, string>;
		files: any[];
		chain: string;
		openRouterModel: string;
		apiKey: string;
		temperature: number;
	}) {
		const firstKey = Object.keys(componentPrompt || {})?.[0];
		// Buscar custom components uma única vez
		const customComponents = await getCustomComponents();

		const componentPromises = userPrompt.map(
			async (promptData: { text: string; ref: number }) => {
				const component =
					componentPrompt?.[promptData.ref === 0 ? firstKey : promptData.ref] || '';
				const systemInstruction = component + chain;
				const prompt = replaceCustomComponents(promptData.text, customComponents);
				const contentParts: any[] = [{ text: prompt }];

				if (files && files.length > 0) {
					console.log('🌀 OPENROUTER CODE - Processando arquivos:', files.length);
					for (const file of files) {
						if (file?.url && !file?.data) {
							try {
								const res = await fetch(file.url);
								const ab = await res.arrayBuffer();
								(file as any).data = Buffer.from(ab).toString('base64');
								(file as any).type =
									file.type || res.headers.get('content-type') || 'application/octet-stream';
							} catch (downloadErr) {
								console.warn(
									'⚠️ OPENROUTER CODE - Falha ao baixar arquivo:',
									file?.name,
									downloadErr
								);
							}
						}

						try {
							const base64Data = file.data || file.url;
							if (!base64Data) {
								console.warn(`⚠️ OPENROUTER CODE - Arquivo sem dados: ${file.name}`);
								continue;
							}

							const cleanBase64 = base64Data.includes('base64,')
								? base64Data.split('base64,')[1]
								: base64Data;

							const isValidBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64);
							if (!isValidBase64) {
								console.error(`❌ OPENROUTER CODE - Base64 inválido para ${file.name}`);
								continue;
							}

							// Mesma whitelist do caminho Gemini para consistência
							const supportedTypes = [
								'image/png',
								'image/jpeg',
								'image/jpg',
								'image/webp',
								'image/gif',
								'application/pdf',
								'text/plain',
								'text/csv',
								'text/html',
								'text/css',
								'text/javascript',
								'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
								'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
								'application/vnd.openxmlformats-officedocument.presentationml.presentation',
								'application/rtf',
								'text/rtf'
							];

							if (supportedTypes.includes(file.type)) {
								contentParts.push({
									inlineData: {
										data: cleanBase64,
										mimeType: file.type
									}
								});
								console.log(
									`✅ OPENROUTER CODE - Arquivo adicionado: ${file.name} (${file.type})`
								);
							} else {
								console.warn(
									`⚠️ OPENROUTER CODE - Tipo não suportado: ${file.type} (${file.name})`
								);
							}
						} catch (procErr) {
							console.error('❌ OPENROUTER CODE - Erro ao processar arquivo:', procErr);
						}
					}
				}

				const userContent: any[] = [];
				for (const part of contentParts) {
					if (part.text) userContent.push({ type: 'text', text: part.text });
					else if (part.inlineData) {
						userContent.push({
							type: 'image_url',
							image_url: {
								url: `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`
							}
						});
					}
				}

				const openRouterPayload: any = {
					model: openRouterModel,
					messages: [
						{ role: 'system', content: systemInstruction },
						{
							role: 'user',
							content:
								userContent.length === 1 && userContent[0].type === 'text'
									? userContent[0].text
									: userContent
						}
					],
					temperature
				};

				const resp = await fetch('https://openrouter.ai/api/v1/chat/completions', {
					method: 'POST',
					headers: {
						Authorization: `Bearer ${apiKey}`,
						'Content-Type': 'application/json',
						'HTTP-Referer': 'https://mitralab.io',
						'X-Title': 'Mitra AI Code'
					},
					body: JSON.stringify(openRouterPayload)
				});
				if (!resp.ok) {
					const errTxt = await resp.text();
					throw createError({
						statusCode: 500,
						statusMessage: `OpenRouter error: ${resp.status} ${errTxt}`
					});
				}
				const json: any = await resp.json();
				const answer = json?.choices?.[0]?.message?.content || '';
				return { ref: promptData.ref, text: answer };
			}
		);
		const resolved = await Promise.all(componentPromises);
		const componentMap: { [key: number]: string } = {};
		resolved.forEach(({ ref, text }) => {
			componentMap[ref] = text;
		});
		return componentMap;
	}
	async function generateContent(
		userPrompt: any[],
		componentPrompt: any,
		files: any[],
		genAI: any,
		chain: any
	) {
		const customComponents = await getCustomComponents();
		const firstKey = Object.keys(componentPrompt)?.[0];
		const componentPromises = userPrompt.map(
			async (promptData: { text: string; ref: number }) => {
				const component =
					componentPrompt?.[promptData.ref === 0 ? firstKey : promptData.ref] || '';
				const systemInstruction = component + chain;
				const model = genAI.getGenerativeModel({
					model: 'gemini-2.5-pro',
					systemInstruction
				});
				const prompt = replaceCustomComponents(promptData.text, customComponents);
				const contentParts: any[] = [{ text: prompt }];

				if (files && files.length > 0) {
					console.log('� GEMINI CODE - Processando arquivos:', files.length);

					for (const file of files) {
						if (file?.url && !file?.data) {
							try {
								const res = await fetch(file.url);
								const ab = await res.arrayBuffer();
								(file as any).data = Buffer.from(ab).toString('base64');
								(file as any).type =
									file.type || res.headers.get('content-type') || 'application/octet-stream';
							} catch {}
						}

						try {
							const base64Data = file.data || file.url;
							if (!base64Data) {
								console.warn(`⚠️ GEMINI CODE - Arquivo sem dados: ${file.name}`);
								continue;
							}

							const cleanBase64 = base64Data.includes('base64,')
								? base64Data.split('base64,')[1]
								: base64Data;

							const isValidBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64);
							if (!isValidBase64) {
								console.error(`❌ GEMINI CODE - Base64 inválido para ${file.name}`);
								continue;
							}

							const supportedTypes = [
								// Imagens
								'image/png',
								'image/jpeg',
								'image/jpg',
								'image/webp',
								'image/gif',
								// Documentos
								'application/pdf',
								'text/plain',
								'text/csv',
								'text/html',
								'text/css',
								'text/javascript',
								// Documentos Office
								'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
								'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
								'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
								// Outros
								'application/rtf',
								'text/rtf'
							];

							if (supportedTypes.includes(file.type)) {
								contentParts.push({
									inlineData: {
										data: cleanBase64,
										mimeType: file.type
									}
								});

								console.log(
									`✅ GEMINI CODE - Arquivo adicionado com sucesso: ${file.name} (${file.type})`
								);
							} else {
								console.warn(
									`⚠️ GEMINI CODE - Tipo de arquivo não suportado: ${file.type} para ${file.name}`
								);
							}
						} catch (error) {
							console.error('❌ GEMINI CODE - Erro ao processar arquivo:', error);
						}
					}
				}

				const result = await model.generateContent(contentParts);
				const response = await result.response;
				const text = response.text();
				return { ref: promptData.ref, text };
			}
		);
		try {
			const resolvedComponents = await Promise.all(componentPromises);
			const componentMap: { [key: number]: string } = {};
			resolvedComponents.forEach(({ ref, text }) => {
				componentMap[ref] = text;
			});
			return componentMap;
		} catch (error) {
			console.error(
				'❌ GEMINI CODE - Erro ao resolver promessas dos componentes:',
				error
			);
		}
	}
	try {
		const body = await readBody(event);

		const { userPrompt, componentPrompt, files } = body;
		// Novos campos opcionais para Anthropic/OpenRouter
		const requestedModel: string = body.model || 'gemini-2.5-pro';
		const forceOpenRouter: boolean = body.useOpenRouter === true;
		const isAnthropicLike = /claude|anthropic/i.test(requestedModel);
		const useOpenRouter = forceOpenRouter || isAnthropicLike;

		if (!userPrompt || userPrompt.length === 0) {
			throw createError({
				statusCode: 400,
				statusMessage: 'System prompt is required'
			});
		}

		// API key: Gemini ou OpenRouter
		let apiKey = '';
		if (useOpenRouter) {
			const HARDCODED_OPENROUTER_KEY =
				'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';
			apiKey = HARDCODED_OPENROUTER_KEY;
		} else {
			apiKey = await getUserGeminiApiKey(body.apiKeyPayload);
		}

		const chain = await getChainFromMitraAPI(
			body.isValidacao ? 'Chain AI Coder Validacao' : 'Chain AI Coder'
		);

		// Mapeamentos simples de modelos amigáveis -> IDs OpenRouter
		const modelMappings: Record<string, string> = {
			'claude-sonnet-4': 'anthropic/claude-sonnet-4',
			'claude-4-sonnet': 'anthropic/claude-sonnet-4',
			'claude-3-5-sonnet': 'anthropic/claude-3.5-sonnet',
			'claude-3.5-sonnet': 'anthropic/claude-3.5-sonnet',
			'claude-3-5-sonnet-latest': 'anthropic/claude-3.5-sonnet',
			'claude-35-sonnet': 'anthropic/claude-3.5-sonnet'
		};
		let openRouterModel = requestedModel;
		if (useOpenRouter) {
			if (modelMappings[openRouterModel])
				openRouterModel = modelMappings[openRouterModel];
			if (!openRouterModel.includes('/') && /claude/i.test(openRouterModel)) {
				openRouterModel = 'anthropic/claude-3.5-sonnet';
			}
		}

		const genAI = !useOpenRouter ? new GoogleGenerativeAI(apiKey) : undefined;

		const result = useOpenRouter
			? await generateContentOpenRouter({
					userPrompt,
					componentPrompt,
					files,
					chain,
					openRouterModel,
					apiKey,
					temperature: body.temperature ?? 0.1
			  })
			: await generateContent(userPrompt, componentPrompt, files, genAI, chain);

		return {
			success: true,
			content: result
		};
	} catch (error: any) {
		// eslint-disable-next-line no-console
		console.error('❌ Erro na chamada Gemini Code:', error);

		throw createError({
			statusCode: 500,
			statusMessage: error.message || 'Erro interno do servidor'
		});
	}
});
