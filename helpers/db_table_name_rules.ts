// helpers/db_table_name_rules.ts
import type { RuleObject } from 'ant-design-vue/es/form/interface';

export type ShouldValidateInput = boolean | (() => boolean);

export function makeMysqlNameRules(
	shouldValidate: ShouldValidateInput
): RuleObject[] {
	const getActive = () =>
		typeof shouldValidate === 'function' ? shouldValidate() : shouldValidate;

	return [
		{
			validator(_rule, value: string) {
				const msg = checkMysqlIdentifier(value, { shouldValidate: getActive() });
				if (msg) throw new Error(msg);
			},
			trigger: ['change', 'blur']
		}
	];
}

export const MAX_NAME_LENGTH = 58;

export function getCharactersRemaining(name: string): number {
	return MAX_NAME_LENGTH - name.length;
}

/**
 * Retorna a chave de tradução e os parâmetros para o i18n
 */
export function getCharacterHintData(remaining: number): {
	key: string;
	params: Record<string, number>;
} {
	if (remaining === MAX_NAME_LENGTH) {
		return {
			key: 'DATABASE.hint_text_character',
			params: { max: MAX_NAME_LENGTH }
		};
	} else if (remaining === 0) {
		return {
			key: 'DATABASE.you_text_hit_max_characters',
			params: { max: MAX_NAME_LENGTH }
		};
	} else {
		return { key: 'DATABASE.characters_remaining', params: { remaining } };
	}
}

export type MysqlNameOptions = {
	/** quando false, a regra não valida nada (segue seu shouldValidate original) */
	shouldValidate: boolean;
};

/**
 * Retorna `null` se válido; ou uma mensagem de erro (string) se inválido.
 * Não lança exceção — fica simples de usar em qualquer lugar.
 */
export function checkMysqlIdentifier(
	value: unknown,
	{ shouldValidate }: MysqlNameOptions
): string | null {
	if (!shouldValidate) return null;

	if (!value) return null; // não é obrigatório aqui

	const name = String(value);

	// 1) tamanho
	if (name.length > 58) return 'Máximo de 58 caracteres.';

	// 2) início com letra ou underscore
	if (!/^[A-Za-z_]/.test(name)) return 'O nome deve começar com letra ou _.';

	// 3) proibição explícita de espaço, /, \ e .
	if (/[/\\.\s]/.test(name))
		return 'Não é permitido espaço, "/", "\\" ou "." no nome.';

	// 4) apenas letras, números e underscore
	if (!/^[A-Za-z0-9_]+$/.test(name)) return 'Use apenas letras, números e _.';

	// 5) não pode ser palavra reservada do MySQL
	if (MYSQL_RESERVED_WORDS.has(name.toUpperCase())) {
		return 'O nome não pode ser uma palavra reservada do MySQL.';
	}

	return null;
}

export const MYSQL_RESERVED_WORDS = new Set([
	'ADD',
	'ALL',
	'ALTER',
	'ANALYZE',
	'AND',
	'AS',
	'ASC',
	'BEFORE',
	'BETWEEN',
	'BIGINT',
	'BINARY',
	'BLOB',
	'BOTH',
	'BY',
	'CALL',
	'CASCADE',
	'CASE',
	'CHANGE',
	'CHAR',
	'CHECK',
	'COLUMN',
	'CONSTRAINT',
	'CREATE',
	'CROSS',
	'CURRENT_DATE',
	'CURRENT_TIME',
	'CURRENT_TIMESTAMP',
	'DATABASE',
	'DATABASES',
	'DAY_HOUR',
	'DAY_MICROSECOND',
	'DAY_MINUTE',
	'DAY_SECOND',
	'DEC',
	'DECIMAL',
	'DEFAULT',
	'DELETE',
	'DESC',
	'DESCRIBE',
	'DISTINCT',
	'DISTINCTROW',
	'DIV',
	'DOUBLE',
	'DROP',
	'DUAL',
	'EACH',
	'ELSE',
	'ELSEIF',
	'ENCLOSED',
	'ESCAPED',
	'EXISTS',
	'EXIT',
	'EXPLAIN',
	'FALSE',
	'FETCH',
	'FLOAT',
	'FOR',
	'FORCE',
	'FOREIGN',
	'FROM',
	'FULLTEXT',
	'GRANT',
	'GROUP',
	'HAVING',
	'HIGH_PRIORITY',
	'HOUR_MICROSECOND',
	'HOUR_MINUTE',
	'HOUR_SECOND',
	'IF',
	'IGNORE',
	'IN',
	'INDEX',
	'INFILE',
	'INNER',
	'INOUT',
	'INSENSITIVE',
	'INSERT',
	'INT',
	'INTEGER',
	'INTERVAL',
	'INTO',
	'IS',
	'ITERATE',
	'JOIN',
	'KEY',
	'KEYS',
	'KILL',
	'LEADING',
	'LEAVE',
	'LEFT',
	'LIKE',
	'LIMIT',
	'LINES',
	'LOAD',
	'LOCALTIME',
	'LOCALTIMESTAMP',
	'LOCK',
	'LONG',
	'LONGBLOB',
	'LONGTEXT',
	'LOOP',
	'LOW_PRIORITY',
	'MASTER_SSL_VERIFY_SERVER_CERT',
	'MATCH',
	'MAXVALUE',
	'MEDIUMBLOB',
	'MEDIUMINT',
	'MEDIUMTEXT',
	'MIDDLEINT',
	'MINUTE_MICROSECOND',
	'MINUTE_SECOND',
	'MOD',
	'MODIFIES',
	'NATURAL',
	'NOT',
	'NO_WRITE_TO_BINLOG',
	'NULL',
	'NUMERIC',
	'ON',
	'OPTIMIZE',
	'OPTION',
	'OPTIONALLY',
	'OR',
	'ORDER',
	'OUT',
	'OUTER',
	'OUTFILE',
	'PRECISION',
	'PRIMARY',
	'PROCEDURE',
	'PURGE',
	'RANGE',
	'READ',
	'READS',
	'REAL',
	'REFERENCES',
	'REGEXP',
	'RELEASE',
	'RENAME',
	'REPEAT',
	'REPLACE',
	'REQUIRE',
	'RESIGNAL',
	'RESTRICT',
	'RETURN',
	'REVOKE',
	'RIGHT',
	'RLIKE',
	'SCHEMA',
	'SCHEMAS',
	'SECOND_MICROSECOND',
	'SELECT',
	'SENSITIVE',
	'SEPARATOR',
	'SET',
	'SHOW',
	'SIGNAL',
	'SMALLINT',
	'SPATIAL',
	'SPECIFIC',
	'SQL',
	'SQLEXCEPTION',
	'SQLSTATE',
	'SQLWARNING',
	'SQL_BIG_RESULT',
	'SQL_CALC_FOUND_ROWS',
	'SQL_SMALL_RESULT',
	'SSL',
	'STARTING',
	'STRAIGHT_JOIN',
	'TABLE',
	'TERMINATED',
	'THEN',
	'TINYBLOB',
	'TINYINT',
	'TINYTEXT',
	'TO',
	'TRAILING',
	'TRIGGER',
	'TRUE',
	'UNDO',
	'UNION',
	'UNIQUE',
	'UNLOCK',
	'UNSIGNED',
	'UPDATE',
	'USAGE',
	'USE',
	'USING',
	'UTC_DATE',
	'UTC_TIME',
	'UTC_TIMESTAMP',
	'VALUES',
	'VARBINARY',
	'VARCHAR',
	'VARCHARACTER',
	'VARYING',
	'WHEN',
	'WHERE',
	'WHILE',
	'WITH',
	'WRITE',
	'XOR',
	'YEAR_MONTH',
	'ZEROFILL'
]);
