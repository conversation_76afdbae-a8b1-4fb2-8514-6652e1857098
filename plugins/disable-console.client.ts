export default defineNuxtPlugin(() => {
	const config = useRuntimeConfig();

	const logsDisabled =
		config.public.DROP_FRONTEND_LOGS === 'true' ||
		// @ts-ignore
		config.public.DROP_FRONTEND_LOGS === true;

	if (logsDisabled) {
		// eslint-disable-next-line no-console
		console.log = () => {};
		// eslint-disable-next-line no-console
		console.warn = () => {};
		// eslint-disable-next-line no-console
		console.info = () => {};
	}
});
