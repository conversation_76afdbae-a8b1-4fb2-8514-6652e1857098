<template>
	<div class="flex items-center py-3 px-4 justify-between">
		<span class="ml-2 text-base font-medium text-grey-900">
			{{ $t('DATABASE.create_database_action') }}
		</span>

		<HugeiconsIcon
			:icon="Cancel01Icon"
			:size="18"
			@click="close"
		/>
	</div>

	<a-divider class="m-0 p-0"></a-divider>

	<div class="p-6 flex flex-col">
		<div class="text-sm font-medium text-grey-700">
			{{ $t('GLOBAL.name') }}
		</div>
		<a-input
			v-model:value="name"
			type="text"
			:placeholder="$t('GLOBAL.insert')"
			class="input-class py-1.5 mt-2"
		>
		</a-input>
	</div>

	<a-divider class="m-0 p-0"></a-divider>

	<div class="flex justify-end space-x-3 p-4">
		<MitraButton
			:text="$t('BUTTONS.cancel')"
			color="var(--violet-600)"
			outlined
			:text-color="
				useWhiteLabel().isSankhyaClient ? 'var(--grey-500)' : 'var(--violet-600)'
			"
			@click="close"
		/>
		<MitraButton
			:disabled="!name"
			data-cy="btn-submit"
			:text="$t('BUTTONS.create')"
			color="var(--violet-600)"
			@click="handleCreateAction"
		/>
	</div>
</template>

<script setup lang="ts">
	import { Cancel01Icon } from '@hugeicons-pro/core-stroke-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';

	const name = ref();

	const metadataStore = useMetadataStore();

	async function handleCreateAction() {
		const data = await metadataStore.saveScript(name.value, true);
		if (data) addItemToDatabase({ ...data, isAction: true });
	}

	async function addItemToDatabase(
		item: (Dimension | Dataset) & { dataSetId?: number }
	) {
		close();
		metadataStore.addOnMetadataList(item);
		await metadataStore.fetchDatabaseList();
	}

	function close() {
		useDialogStore().switchDialog('createActionTableMenuController', false);
	}
</script>

<style lang="postcss" scoped>
	.input-class {
		border: 1px solid #c7cbdd;
		box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
		border-radius: 8px;
	}
	.input-class::placeholder {
		color: var(--grey-500);
		font-weight: 400;
		font-size: 14px;
	}
	:deep(.ant-select-selection-item) {
		color: var(--grey-900);
		font-size: 14px;
		font-weight: 400;
		font-weight: 24px;
		height: 60px;
	}
	:deep(
			.ant-select-single:not(.ant-select-customize-input) .ant-select-selector
		) {
		height: 44px;
		box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
		border-radius: 8px;
		border: 1px solid #c7cbdd;
		align-items: center;
	}

	:deep(
			.ant-select-single.ant-select-show-arrow .ant-select-selection-item,
			.ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder
		) {
		display: flex;
		height: 44px;
		align-items: center;
		font-weight: 400;
		font-size: 14px;
		color: var(--grey-900);
	}

	:deep(.ant-select-selector) {
		.ant-select-selection-placeholder {
			color: var(--grey-500);
		}
	}

	:deep(.ant-select-arrow) {
		top: 48%;
		right: 18px;
	}

	:deep(.ant-select-arrow svg) {
		width: 18px;
		height: 18px;
	}
</style>
