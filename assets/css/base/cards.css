@layer components {
	.simple-square {
		@apply h-[40px] w-[40px];
	}

	.elevated-card {
		transition: all 0.3s ease;
		background: var(--white);
		cursor: pointer;
		border-radius: 8px;
		border: 1px solid var(--grey-200);
	}

	.icon-badge {
		border-radius: 8px;
		background-color: var(--violet-100);
		width: 52px;
		height: 52px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.elevated-card:hover {
		box-shadow: 0 0.48px 0.96px rgba(0, 0, 0, 0.2);
	}

	.new-skill-panel {
		@apply h-[80vh] overflow-auto;
	}
}
