export default function syncURLVariable() {
	try {
		const selectedProject = useWorkspaceStore().selectedProject;
		const workspaceId = selectedProject.workspaceId;
		const isLocalhost =
			window.location.hostname === 'localhost' || window.location.hostname === '';
		if (
			selectedProject.id &&
			workspaceId &&
			!isLocalhost &&
			!useWhiteLabel().isSankhyaClient &&
			process.env.NODE_ENV === 'production'
		) {
			const { getProjectAddress } = storeToRefs(useMarketplacePublisher());
			const address = getProjectAddress.value;
			const projectId = selectedProject.id;
			const url = address
				? `https://${address}/w/${workspaceId}/p/${projectId}`
				: `${window.location.origin}/w/${workspaceId}/p/${projectId}`;
			useProjectService().setPublicURLVariable({ content: url });
		}
	} catch (error) {
		// eslint-disable-next-line no-console
		console.error('Error in sync-project-url-variables middleware:', error);
	}
}
