<template>
	<a-modal
		v-model:visible="knowledgeBaseController"
		destroy-on-close
		width="490px"
		centered
		z-index="9999"
		:footer="null"
		@cancel="onClosed"
	>
		<div class="flex items-center justify-center">
			<div
				class="alert-icon-outside m-0.5 flex items-center rounded-full px-2 py-2"
			>
				<div
					class="alert-icon-inside m-0.5 flex items-center rounded-full px-2 py-2"
				>
					<svg-icon
						color="#D92D20"
						:path="mdiAlertCircleOutline"
						type="mdi"
						size="24px"
					></svg-icon>
				</div>
			</div>
		</div>
		<div
			class="flex flex-col items-center justify-center pb-8 pt-5 text-center"
		>
			<div class="pb-2 text-lg font-medium text-grey-900">
				{{
					isCurrentKnowledgeBase
						? $t('GLOBAL.remove_knowledge_base', {
								name: getActiveProject.name
						  })
						: $t('GLOBAL.make_knowledge_base', {
								name: getActiveProject.name
						  })
				}}
			</div>
			<div class="items-center text-sm font-medium text-grey-500">
				<div
					v-if="!isCurrentKnowledgeBase"
					class="text-sm text-grey-500 font-normal"
				>
					{{ $t('GLOBAL.make_knowledge_base_message') }}
				</div>
				<div
					v-else
					class="text-sm text-grey-500 font-normal"
				>
					{{ $t('GLOBAL.remove_knowledge_base_message') }}
				</div>
			</div>
		</div>
		<div class="gap-4 flex w-full items-center">
			<MitraButton
				:text="$t('BUTTONS.cancel')"
				text-color="var(--grey-700)"
				outlined
				w-full
				@click="
					onClosed();
					useDialogStore().switchDialog('knowledgeBaseController');
				"
			>
			</MitraButton>
			<MitraButton
				:text="$t('BUTTONS.confirm')"
				:color="
					!isCurrentKnowledgeBase ? 'var(--violet-600)' : 'var(--error-600)'
				"
				w-full
				:loading="loading"
				@click="
					isCurrentKnowledgeBase
						? emit('knowledge-remove', getActiveProject.id)
						: emit('knowledge-project', getActiveProject.id)
				"
			>
			</MitraButton>
		</div>
	</a-modal>
</template>

<script setup lang="ts">
	import { mdiAlertCircleOutline } from '@mdi/js';
	const dialogStore = useDialogStore();
	const workspaceStore = useWorkspaceStore();
	const { knowledgeBaseController } = storeToRefs(dialogStore);
	const { selectedProject, temporarySelectedProject } =
		storeToRefs(workspaceStore);

	const isCurrentKnowledgeBase = computed(() => {
		if (
			workspaceStore &&
			workspaceStore.workspaceInfo &&
			workspaceStore.workspaceInfo.knowledgeBase &&
			workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId
		) {
			return (
				workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId ===
				getActiveProject.value.id
			);
		}
		return false;
	});

	const emit = defineEmits(['knowledge-project', 'knowledge-remove']);

	const getActiveProject = computed(() => {
		return temporarySelectedProject.value.id
			? temporarySelectedProject.value
			: selectedProject.value;
	});

	defineProps({
		loading: { type: Boolean, required: false, default: false }
	});

	const onClosed = () => {
		workspaceStore.resetTemporarySelectedProject();
	};
</script>

<style lang="postcss" scoped>
	.alert-icon-outside {
		background-color: #fef3f2;
	}
	.alert-icon-inside {
		background-color: #fee4e2;
	}

	.delete-button {
		background: #d92d20 !important;
	}
</style>
