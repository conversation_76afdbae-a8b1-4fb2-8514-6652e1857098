import { GLOBALS } from '~/helpers/contants/global_constants';

export function useAIKey() {
	const localUseAIKey = ref(false);
	const savingUseAIKey = ref(false);
	const { userIsCorpAndUseOwnKey, hasWorkspaceKey, workspaceAISettings } =
		storeToRefs(useSuperAIStore());

	const { selectedWorkspace, isCorporateWorkspace } = storeToRefs(
		useWorkspaceStore()
	);

	async function saveUseAIKey(value: boolean) {
		savingUseAIKey.value = true;
		try {
			try {
				await useSuperAIStore().changeConsumptionAIMode(value);
				localUseAIKey.value = value;
			} catch (error) {
				// eslint-disable-next-line no-console
				console.log('error', error);
			} finally {
				savingUseAIKey.value = false;
			}
		} catch (error) {
			// eslint-disable-next-line no-console
			console.log('error', error);
		} finally {
			savingUseAIKey.value = false;
		}
	}

	function setLocalUseAIKey() {
		const key =
			useSuperAIStore().getKeyByModelId(GLOBALS.AI_MODELS.GEMINI.id) ?? '';

		if (useWhiteLabel().isSankhyaClient) {
			if (
				workspaceAISettings.value?.fallbackAIKey &&
				userIsCorpAndUseOwnKey.value &&
				key.length
			) {
				userIsCorpAndUseOwnKey.value = true;
				localUseAIKey.value = true;
			} else {
				userIsCorpAndUseOwnKey.value = false;
				localUseAIKey.value = false;
			}
		}

		if (!workspaceAISettings.value?.openaiAccessKey) {
			localUseAIKey.value = true;
			if (isCorporateWorkspace.value) {
				userIsCorpAndUseOwnKey.value = true;
			}
		} else {
			localUseAIKey.value = userIsCorpAndUseOwnKey.value;
		}
	}

	setLocalUseAIKey();

	watch(
		() => selectedWorkspace.value,
		() => {
			setLocalUseAIKey();
		}
	);

	return {
		saveUseAIKey,
		savingUseAIKey,
		localUseAIKey,
		hasWorkspaceKey
	};
}
