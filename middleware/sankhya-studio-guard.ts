import { useCookies } from '@vueuse/integrations/useCookies.mjs';
import { ROUTE_PATHS } from '~~/helpers/route_paths';

export default defineNuxtRouteMiddleware((to, _from) => {
	const sankhyaStore = useSankhyaStore();
	const workspaceId = parseInt(to.query.workspaceId as string);

	useCookies().set('isSankhya', 'true', {
		path: '/',
		domain: '.mitralab.io', // ← permite leitura em todos os subdomínios
		maxAge: 60 * 2, // 2 minutos
		sameSite: 'none',
		secure: true // obrigatório com SameSite=None
	});

	if (isNaN(workspaceId)) {
		const { workspaceRequisitionBody, mitraSpaceToken, mitraSpaceBackUrl } =
			to.query;

		if (workspaceRequisitionBody) {
			try {
				const payload = JSON.parse(String(workspaceRequisitionBody));

				sankhyaStore.setWorkspaceRequestPayload({
					mitraSpaceBackUrl: String(mitraSpaceBackUrl),
					mitraSpaceToken: `Bearer ${mitraSpaceToken}`,
					workspaceRequisitionBody: payload
				});
				return navigateTo(ROUTE_PATHS.SANKHYA_NO_LICENSES);
			} catch (error) {
				// eslint-disable-next-line no-console
				console.error('Falha ao processar o corpo da requisição do workspace:', error);
				// Opcional: redirecionar para uma página de erro genérica
			}
		}
	}
});
