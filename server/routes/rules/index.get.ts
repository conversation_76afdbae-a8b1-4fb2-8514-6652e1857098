import axios from 'axios';

export default defineEventHandler(async (event) => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const { _dummy } = getQuery(event);
	const { MITRA_CONTROLLER_TOKEN } = useRuntimeConfig();

	const axiosInstance = axios.create({
		baseURL: 'https://api.mitrasheet.com:5541/rest/v0',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${MITRA_CONTROLLER_TOKEN}`
		}
	});

	try {
		const { data: developerData } = await axiosInstance.get(`/DeveloperBypass`);

		const { data: enterpriseData } = await axiosInstance.get(`/Enteprise Bypass`);

		const developerContent = (developerData as any)?.content ?? [];
		const enterpriseContent = (enterpriseData as any)?.content ?? [];

		return {
			developerBypassList: developerContent.map((item: any) => ({
				workspaceId: Math.trunc(Number(item.devWorkspaceId)),
				projectId: Math.trunc(Number(item.devProjectId))
			})),
			entepriseBypassedWorkspaces: enterpriseContent.map((item: any) => ({
				workspaceId: Math.trunc(Number(item.workspaceId)),
				projectId: Math.trunc(Number(item.projectId))
			}))
		};
	} catch (error) {
		// eslint-disable-next-line no-console
		console.error('Failed to fetch developer bypass list:', error);

		throw createError({
			statusCode: 500,
			statusMessage: 'Failed to fetch developer bypass list'
		});
	}
});
