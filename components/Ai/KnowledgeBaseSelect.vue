<template>
	<div class="flex items-center gap-2 pa-2 mr-2">
		<span class="tags-text"> {{ $t('GLOBAL.linked_tags') }}: </span>

		<a-popover
			v-model:visible="tagListController"
			overlay-class-name="w-[200px] mitra-base-popover mt-2"
			trigger="click"
			placement="topLeft"
		>
			<div
				class="flex items-center justify-center cursor-pointer tags-link-wrapper"
				@click.stop.prevent="handleTagListDialog"
			>
				<HugeiconsIcon
					:icon="Add01Icon"
					size="16"
					color="var(--grey-900)"
					class="shrink-0"
				></HugeiconsIcon>
				<span
					v-if="!localSelectedTags.length"
					class="tags-link"
				>
					{{ $t('GLOBAL.link_tags') }}
				</span>
			</div>

			<template #content>
				<div
					v-if="tags.length > 1"
					class="cursor-pointer check-all"
				>
					<a-checkbox
						color="var(--grey-900)"
						@change="() => toggleTag()"
					/>
					<span class="check-all-text">
						{{ $t('GLOBAL.check_all') }}
					</span>
				</div>

				<div
					v-for="tag in tags"
					:key="tag.name"
					class="cursor-pointer tags-list"
					:style="
						localSelectedTags.some((t) => t.name === tag.name)
							? 'background: var(--Gray-50, #f6f7fb)'
							: ''
					"
					@click="handleTagSelected(tag)"
				>
					<a-checkbox
						:checked="localSelectedTags.some((t) => t.name === tag.name)"
						color="var(--grey-900)"
					/>
					<a-tag
						:color="tag.color"
						:value="tag.name"
						class="know-tag"
					>
						{{ tag.name }}
					</a-tag>
				</div>
			</template>
		</a-popover>

		<div class="flex gap-2">
			<a-tag
				v-for="tag in localSelectedTags"
				:key="tag.name"
				:color="tag.color"
				:value="tag.name"
				class="know-tag"
			>
				{{ tag.name }}
			</a-tag>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { withDefaults } from 'vue';
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { Add01Icon } from '@hugeicons-pro/core-stroke-rounded';
	// import { useMetadataStore } from '~/store/database/metadata_store';

	const tagListController = ref(false);

	const props = withDefaults(
		defineProps<{
			tableName?: {
				name?: string;
				isFreeDbOnlineTable?: boolean;
				id?: number;
			} | null;
			onlineTags?: {
				id: number | null;
				name: string;
				color: string;
				createdAt: Date | null;
				createdBy: string | null;
				updatedAt: Date | null;
				updatedBy: string | null;
			}[];
		}>(),
		{
			tableName: null,
			onlineTags: () => []
		}
	);

	const tags = ref<
		{
			id: number | null;
			name: string;
			color: string;
			createdAt: Date | null;
			createdBy: string | null;
			updatedAt: Date | null;
			updatedBy: string | null;
		}[]
	>([]);

	const localSelectedTags = ref<
		{
			id: number | null;
			name: string;
			color: string;
			createdAt: Date | null;
			createdBy: string | null;
			updatedAt: Date | null;
			updatedBy: string | null;
		}[]
	>([]);

	watch(
		() => props.onlineTags,
		(tags: any[]) => {
			localSelectedTags.value = tags;
		},
		{ deep: true }
	);

	const toggleTag = () => {
		if (localSelectedTags.value.length === tags.value.length) {
			localSelectedTags.value = [];
		} else {
			localSelectedTags.value = [...tags.value];
		}
		localSelectedTags.value.forEach((tag: any) => {
			handleTagSelected(tag);
		});
	};

	const changeStatus = (tag: any) => {
		const exists = localSelectedTags.value.some(
			(t: { name: any }) => t.name === tag.name
		);
		if (exists) {
			localSelectedTags.value = localSelectedTags.value.filter(
				(t: {
					id: number | null;
					name: string;
					color: string;
					createdAt: Date | null;
					createdBy: string | null;
					updatedAt: Date | null;
					updatedBy: string | null;
				}) => t.name !== tag.name
			);
		} else {
			localSelectedTags.value.push(tag);
		}
	};

	function handleTagListDialog() {
		loadTagList();
		tagListController.value = true;
	}

	async function handleTagSelected(tag: any) {
		if (props.tableName?.isFreeDbOnlineTable) {
			const payload = {
				queryAliasId: props.tableName?.id,
				tagId: tag.id
			};
			await useLegacyService().createOnlineTableTagRelationship(payload);
			changeStatus(tag);
		} else {
			const payload = {
				tableName: props.tableName?.name,
				tagId: tag.id
			};
			await useLegacyService().createTableTagRelationship(payload);
		}
		loadSelectedTagList();
	}

	loadSelectedTagList();

	async function loadTagList() {
		const { data } = await useLegacyService().findAllTagsPaginated();
		tags.value = data.tags;
	}

	async function loadSelectedTagList() {
		if (!props.tableName?.isFreeDbOnlineTable) {
			if (props.tableName?.name) {
				const { data } = await useLegacyService().getTagByTableName(
					props.tableName.name
				);
				localSelectedTags.value = data;
			}
		}
	}
</script>

<style scoped lang="postcss">
	.know-tag {
		border-radius: 100px;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0;
	}
	.tags {
		&-text {
			color: var(--Gray-700, #303854);
			font-family: Inter;
			font-size: 13px;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
		}
		&-list {
			display: flex;
			padding: 8px 14px;
			align-items: center;
			gap: 8px;
			align-self: stretch;
		}
		&-link-wrapper {
			border-radius: 8px;
			background: var(--Gray-50, #f6f7fb);
			display: flex;
			padding: 2px 6px;
			justify-content: center;
			align-items: center;
			gap: 4px;
		}
		&-link {
			color: var(--Gray-500, #5d6585);
			font-family: Inter;
			font-size: 12.5px;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
		}
	}

	.check-all {
		display: flex;
		padding: 8px 14px;
		align-items: center;
		gap: 8px;
		align-self: stretch;
		border-bottom: 1px solid var(--Gray-200, #e7e8f0);

		&-text {
			color: var(--Gray-900, #0e1428);
			font-family: Inter;
			font-size: 13px;
			font-style: normal;
			font-weight: 400;
			line-height: 20px;
			padding: 0 4px;
		}
	}

	:deep(.ant-select-item-option) {
		gap: 8px !important;
	}
	:deep(.ant-select-selector) {
		height: auto !important;
	}
	:deep(.ant-checkbox-checked .ant-checkbox-inner::after) {
		border-color: var(--violet-600);
	}
</style>
