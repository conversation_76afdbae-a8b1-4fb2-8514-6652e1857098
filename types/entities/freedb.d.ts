export declare global {
	interface DataLoaderForm {
		readonly id?: number;
		name: string;
		workspaceId: number;
		projecId: number;
		type: string;
		tableName?: string;
		uri?: string;
		csvFieldsSeparator?: string;
		query?: string;
		jdbcConnectionConfigId?: number;
		loadJdbcConnectionConfigId?: number;
		lastExecutionId?: number;
		lastExecutionStatus?: string;
	}

	interface DataLoaderLog {
		createTableStmt: string;
		csvFileSize?: string;
		csvRetrievalDuration?: string;
		csvRetrievalEndAt?: string;
		csvRetrievalStartAt?: string;
		dataLoaderId: number;
		status: string;
		filledQuery: string;
		startAt: string;
		endAt: string;
		errorMsg?: string;
		tableRowCount: number;
		csvRowCount?: number;
		csvSizeBytes?: number;
		executionDuration: string;
		userEmail: string;
		loadDataDuration?: string;
		loadDataEndAt?: string;
		loadDataStartAt?: string;
	}
}
