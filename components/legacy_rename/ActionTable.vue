<template>
	<InteractionLegacyExecuteQuery
		:from-db="true"
		is-action-from-db
		:class="{ 'mt-[16px]': selectedJdbcProperties.asFreeDB }"
	/>
</template>

<script lang="ts" setup>
	const props = defineProps({
		dataset: {
			type: Object as PropType<Dataset & { readerQueryId?: number }>,
			required: true
		}
	});
	useDialogStore().dataToDialog('interactionLegacyExecuteQueryData', {
		queryId: props.dataset.id
	});

	const connectionStore = useConnectionStore();
	const { selectedJdbcProperties } = storeToRefs(connectionStore);
</script>

<style lang="postcss" scoped></style>
