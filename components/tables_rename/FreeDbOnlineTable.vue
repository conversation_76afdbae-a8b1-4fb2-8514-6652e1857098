<template>
	<div
		class="dataset-table relative flex w-full flex-col overflow-hidden bg-white !l"
		:class="{ 'p-2': !loadingTableContent }"
	>
		<div
			v-if="loadingTableContent"
			class="absolute z-20 h-full w-full bg-grey-300"
		>
			<div class="h-full w-full animate-pulse bg-grey-200"></div>
		</div>

		<div
			v-if="loadingTableContent && errorOnloadingTableContent"
			class="absolute z-20 h-full w-full bg-white"
		>
			<div class="h-full w-full items-center justify-center flex">
				<div class="flex items-center flex-col gap-2">
					<div
						class="rounded-lg h-[52px] w-[52px] flex items-center justify-center"
						:class="{
							'bg-[--sankhya-green-50]': useWhiteLabel().isSankhyaClient,
							'bg-[--violet-100]': !useWhiteLabel().isSankhyaClient
						}"
					>
						<img
							src="~/assets/images/mockups/close_eye.svg?componentText"
							alt="csvIcon 'w-[20px] h-[20px]"
						/>
					</div>
					<div class="text-xl text-grey-900 font-medium pt-4">
						{{ $t('DATABASE.preview_not_available') }}
					</div>
					<div class="text-base text-grey-600 font-normal max-w-[513px] text-center">
						{{ $t('DATABASE.data_too_large_to_display') }}
					</div>
				</div>
			</div>
		</div>
		<div
			v-if="queryPreview?.queryResult?.length"
			class="w-full overflow-auto rounded-lg border border-grey-200 h-full bg-grey-25"
		>
			<!-- HEADER -->
			<div
				class="sticky top-0 z-10 bg-grey-50 border-b border-grey-200 text-sm text-grey-700 flex"
			>
				<div
					v-for="(col, i) in queryPreview.queryResult[0]"
					:key="'h' + i"
					class="p-2 font-medium flex items-center gap-2 whitespace-nowrap border-0 border-b border-grey-200 border-solid w-full max-w-[150px]"
					:class="{
						'border-l': i !== 0,
						'border-r': i === queryPreview.queryResult[0].length - 1
					}"
				>
					<!-- ícone placeholder -->
					<HugeiconsIcon
						:icon="hugeIcons[hugeKeyForIndex(i)]"
						:color="'var(--grey-600)'"
						class="item-huge-icon"
						:size="17"
					/>
					<div class="truncate text-sm font-normal text-grey-900">{{ col }}</div>
				</div>
			</div>

			<!-- BODY -->
			<div class="divide-y divide-grey-100">
				<div
					v-for="(row, rIdx) in queryPreview.queryResult.slice(1)"
					:key="'r' + rIdx"
					class="flex"
				>
					<div
						v-for="(cell, cIdx) in row"
						:key="'c' + rIdx + '-' + cIdx"
						class="p-2 text-sm text-grey-900 border-0 border-b border-grey-200 border-solid w-full max-w-[150px]"
						:class="{ 'border-l': cIdx !== 0, 'border-r': cIdx === row.length - 1 }"
					>
						<!-- numéricos: sem pílula -->

						<div
							v-if="isNumberCell(cell)"
							class="font-normal text-sm text-grey-900 h-full items-center flex"
						>
							{{ formatNumber(cell) }}
						</div>

						<!-- textos: pílula com borda -->

						<div
							v-else
							class="inline-flex items-center gap-1 rounded-md border border-grey-300 bg-grey-100 px-2 border-solid max-w-full"
						>
							<div
								class="truncate text-xs font-normal text-grey-900"
								:title="String(cell)"
							>
								{{ cell }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="h-[32px] light-border-t"></div>
	</div>
</template>
<script setup lang="ts">
	import { HugeiconsIcon } from '@hugeicons/vue';
	const props = defineProps({
		loadOnCreated: {
			type: Boolean,
			default: false
		},
		dataset: {
			type: Object as PropType<Dataset>,
			required: true
		},
		freeOnlineTable: {
			type: Object as PropType<FreeDbOnlineTableForm>,
			required: true
		},
		displayName: {
			type: Boolean,
			default: true
		},
		fixed: {
			type: Number,
			default: 0
		},
		search: {
			type: String,
			default: ''
		},
		isDynamic: {
			type: Boolean,
			required: false,
			default: false
		}
	});

	const loadingTableContent = ref(false);
	const errorOnloadingTableContent = ref(false);
	const queryPreview = ref<{
		headers: any[];
		queryResult: any[];
	}>();

	const hugeIcons = useDataTypeHugeIcons();

	type HugeKey = keyof ReturnType<typeof useDataTypeHugeIcons>;

	const attTypes: Record<string, HugeKey> = {
		INT: 'INT',
		INTEGER: 'INT',
		BIGINT: 'INT',
		DOUBLE: 'Number',
		DECIMAL: 'Number',
		NUMERIC: 'Number',
		FLOAT: 'Number',
		REAL: 'Number',
		VARCHAR: 'Text',
		CHAR: 'Text',
		TEXT: 'TEXT',
		DATE: 'Date',
		DATETIME: 'DateTime',
		TIMESTAMP: 'DateTime'
	};

	function hugeKeyFor(dt?: string, dimType?: HugeKey): HugeKey {
		if (dimType && hugeIcons[dimType]) return dimType;
		const key = (dt || '').toUpperCase();
		return (attTypes[key] as HugeKey) || 'Text';
	}

	// nome da coluna -> dataType (UPPER)
	const typeByColName = computed<Record<string, string>>(() => {
		const map: Record<string, string> = {};
		(queryPreview.value?.headers ?? []).forEach((h) => {
			if (h?.name) map[h.name] = String(h.dataType || '').toUpperCase();
		});
		return map;
	});

	// dado o índice da coluna, retorna a HugeKey correta
	function hugeKeyForIndex(colIndex: number): HugeKey {
		const colName = queryPreview.value?.queryResult?.[0]?.[colIndex];
		const dt = colName ? typeByColName.value[String(colName)] : '';
		return hugeKeyFor(dt);
	}

	// helpers simples para renderizar células
	const isNumberCell = (v: unknown) =>
		typeof v === 'number' ||
		(typeof v === 'string' && v.trim() !== '' && !isNaN(Number(v)));

	const formatNumber = (v: unknown) => {
		const n = Number(v);
		return isNaN(n) ? String(v ?? '') : n.toString(); // ajuste se quiser locale
	};

	initData();
	async function initData() {
		loadingTableContent.value = true;
		// Como nao tem get por ID, pego todos freeDbOnlineTable Existentes
		const { freeDbOnlineTableList } =
			await useFreeDbService().getFreeDbOnlineTables();
		if (freeDbOnlineTableList) {
			// procuro o de mesmo ID
			const tableRef = freeDbOnlineTableList.find(
				(item: any) => item.id === props.freeOnlineTable.id
			);
			if (tableRef) {
				// Agora eu carrego os valores do preview dele.
				const { data, error } = await useLegacyService().runViewQueryPreview({
					jdbcConnectionConfigId: tableRef.jdbcConnectionConfigId,
					useOnlineTables: false,
					query: tableRef.query
				});
				if (error) {
					errorOnloadingTableContent.value = true;
					return;
				}
				queryPreview.value = data;
			} else {
				errorOnloadingTableContent.value = true;
				return;
			}
		} else {
			errorOnloadingTableContent.value = true;
			return;
		}

		loadingTableContent.value = false;
	}
</script>
<style lang="postcss" scoped></style>
