<template>
	<div class="flex h-[92vh] flex-col overflow-hidden bg-[#F8F9FD]">
		<div class="border-bottom flex shrink-0 justify-between bg-white px-4 py-3">
			<div class="flex flex-col justify-center">
				<div class="flex text-lg font-medium text-grey-900">
					{{ $t('PROJECT.json_config') }}
				</div>
			</div>
			<div class="flex items-center gap-2">
				<MitraButton
					class="h-[28px]"
					outlined
					type-text
					text-color="var(--grey-500)"
					:text="$t('BUTTONS.cancel')"
					@click="emit('close')"
				/>
				<MitraButton
					class="h-[28px]"
					:text="$t('BUTTONS.save')"
					:color="
						useWhiteLabel().isSankhyaClient
							? 'var(--sankhya-green-600)'
							: 'var(--violet-600)'
					"
					@click="saveData"
				/>
			</div>
		</div>

		<div class="h-full p-4 flex overflow-hidden w-full">
			<div class="content-area w-full">
				<div class="table-area flex flex-col overflow-y-auto">
					<div class="bg-white overflow-auto">
						<div
							v-if="localData.headers.length > 0"
							class="flex border-bottom header"
						>
							<div
								v-for="header in localData.headers"
								:key="header"
								class="flex-1 py-2 text-sm font-semibold text-gray-500 item h-gray"
							>
								<div
									class="truncate"
									:title="header"
								>
									{{ header }}
								</div>
							</div>
							<div class="flex-none w-12 item delete-icon h-gray"></div>
						</div>
						<div class="w-fit rounded-lg">
							<div v-if="localData.value.length > 0">
								<div
									v-for="(row, rowIndex) in localData.value"
									:key="rowIndex"
									class="flex border-t border-gray-200 first:border-t-0 border-bottom"
								>
									<div
										v-for="header in localData.headers"
										:key="header"
										class="flex-1 cursor-pointer item"
										@click="startEditing(rowIndex, header)"
									>
										<input
											v-if="editingCell.rowIndex === rowIndex && editingCell.header === header"
											ref="editInput"
											v-model="editingText"
											class="w-full text-grey-800 outline-none resize-none bg-transparent"
											@blur="stopEditing"
											@keydown.enter.prevent="stopEditing"
										/>
										<div
											v-else
											class="text-sm text-grey-800 truncate"
											:title="formatCellContent(row[header])"
										>
											{{ formatCellContent(row[header]) }}
										</div>
									</div>
									<div
										class="flex-none w-12 item flex items-center justify-center cursor-pointer delete-icon"
										@click="deleteRow(row)"
									>
										<HugeiconsIcon
											:icon="Delete01Icon"
											class="item-huge-icon delete-hover-icon"
											type="rounded"
											color="var(--grey-500)"
											:size="20"
										/>
									</div>
								</div>
								<div class="ghost-cell"></div>
							</div>
							<div
								class="flex cursor-pointer items-center gap-2 px-4 py-3 text-sm font-medium text-violet-600 hover:bg-violet-50/50 border-bottom"
								:class="{ 'border-t border-gray-200': localData.value.length > 0 }"
								@click="addNewRow"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2.5"
									stroke-linecap="round"
									stroke-linejoin="round"
								>
									<line
										x1="12"
										y1="5"
										x2="12"
										y2="19"
									></line>
									<line
										x1="5"
										y1="12"
										x2="19"
										y2="12"
									></line>
								</svg>
								Nova linha
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { ref, reactive, watchEffect, nextTick, computed } from 'vue';
	import { Delete01Icon } from '@hugeicons-pro/core-stroke-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { GLOBALS } from '~~/helpers/contants/global_constants';

	const emit = defineEmits(['close']);

	const props = defineProps({
		variableData: {
			type: Object,
			required: true,
			default: () => ({ headers: [], value: [] })
		}
	});

	const localData = reactive({
		headers: [] as string[],
		value: [] as Record<string, any>[]
	});

	const editingCell = ref<{ rowIndex: number | null; header: string | null }>({
		rowIndex: null,
		header: null
	});
	const editInput = ref<HTMLTextAreaElement[] | null>(null);

	const formatCellContent = (content: any): string => {
		if (typeof content === 'object' && content !== null) {
			return JSON.stringify(content);
		}
		return String(content ?? '');
	};

	initValue();

	const editingText = computed({
		get() {
			const { rowIndex, header } = editingCell.value;
			if (rowIndex === null || header === null) return '';

			const value = localData.value[rowIndex][header];
			if (typeof value === 'object' && value !== null) {
				try {
					return JSON.stringify(value, null, 2);
				} catch {
					return '[Circular Object]';
				}
			}
			return String(value ?? '');
		},
		set(newValue: string) {
			const { rowIndex, header } = editingCell.value;
			if (rowIndex === null || header === null) return;

			try {
				localData.value[rowIndex][header] = JSON.parse(newValue);
			} catch (e) {
				localData.value[rowIndex][header] = newValue;
			}
		}
	});

	watchEffect(() => {
		localData.headers = localData.value.length
			? Array.from(
					localData.value?.reduce((keys, item) => {
						if (item && typeof item === 'object') {
							Object.keys(item).forEach((k) => keys.add(k));
						}
						return keys;
					}, new Set()) as string[]
			  )
			: JSON.parse(JSON.stringify(props.variableData.headers || []));
	});

	function initValue() {
		try {
			if(!localData.value.length) {
				// eslint-disable-next-line no-eval
				const parsedValue = eval(props.variableData.value);
				localData.value =  Array.isArray(parsedValue) ? parsedValue : [];
			}
		} catch {
			localData.value = [];
		}
	}

	const startEditing = async (rowIndex: number, header: string) => {
		editingCell.value = { rowIndex, header };
		await nextTick();
		if (editInput.value && editInput.value.length > 0) {
			editInput.value[0]?.focus();
		}
	};

	const stopEditing = () => {
		editingCell.value = { rowIndex: null, header: null };
	};

	const deleteRow = (rowToDelete: Record<string, any>) => {
		const index = localData.value.findIndex((row) => row === rowToDelete);
		if (index > -1) {
			localData.value.splice(index, 1);
		}
	};

	const addNewRow = () => {
		const newRow: Record<string, string> = {};
		localData.headers.forEach((header) => {
			newRow[header] = '';
		});
		localData.value.push(newRow);
		startEditing(localData.value.length - 1, localData.headers[0]);
	};

	function saveData() {
		const iframeWindow = useIframe().getIframeWindow(GLOBALS.LEGACY_FRAME_ID);
		iframeWindow?.postMessage(
			JSON.stringify({
				typePostMessage: 'saveHtmlVariable',
				variable: { ...props.variableData, value: JSON.stringify(localData.value) }
			}),
			'*'
		);
		emit('close');
	}
</script>

<style lang="postcss" scoped>
	.border-bottom {
		border-bottom: 1px solid var(--grey-200);
	}
	.content-area {
		border-radius: 8px;
		border: 1px solid var(--grey-100);
		background: #fff;
		box-shadow: 0px 1px 3px 0px rgba(16, 24, 40, 0.1),
			0px 1px 2px 0px rgba(16, 24, 40, 0.06);
		height: -webkit-fill-available;
		padding: 16px;
	}
	.table-area {
		border-radius: 8px;
		border: 1px solid var(--grey-200);
		background: var(--grey-25);
		height: -webkit-fill-available;
		display: flex;
	}
	.item {
		display: flex;
		align-items: center;
		height: 32px;
		padding: 0 16px;
		overflow: hidden;
		min-width: 200px;
		max-width: 200px;
	}
	.header .item {
		height: 36px;
		min-width: 200px;
		max-width: 200px;
	}
	.delete-icon {
		min-width: 40px !important;
		position: sticky;
		right: 0;
		background: white;
	}
	.ghost-cell {
		min-width: 40px !important;
		visibility: hidden;
		pointer-events: none;
	}
	.item:not(:last-child) {
		border-right: 1px solid var(--grey-200);
	}
	input {
		outline: none;
		border: none;
		padding: 0px;
	}
	.h-gray {
		background-color: var(--grey-25);
	}
	.w-fit {
		width: fit-content;
	}
	.header {
		position: sticky;
		top: 0;
		z-index: 1;
	}
</style>
