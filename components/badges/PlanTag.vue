<template>
	<div
		class="plan-tag flex gap-2 items-center h-[22px]"
		:style="tagStyle"
		:class="{
			'plan-tag--as-btn': isFreePlan
		}"
		@click="isFreePlan && openUpgradePlan()"
	>
		<span
			class="leading-[18px]"
			:style="{
				color: isFreePlan ? 'var(--grey-500)' : 'var(--violet-600)'
			}"
			>{{ planTag }}</span
		>
		<div
			v-if="isFreePlan"
			class="flex items-center gap-1 medium-border-l pl-2 my-1"
		>
			<HugeiconsIcon
				:icon="SparklesIcon"
				size="14"
				color="var(--violet-600)"
				class="shrink-0"
			></HugeiconsIcon>
			<span class="text-violet-600 font-medium text-xs uppercase">
				{{ width < 1340 ? 'UPGRADE' : $t('GLOBAL.do_upgrade') }}
			</span>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { SparklesIcon } from '@hugeicons-pro/core-stroke-rounded';
	import { useWindowSize } from '@vueuse/core';

	const { customAppConfig } = storeToRefs(useWorkspaceStore());

	const { width } = useWindowSize();

	const props = defineProps<{
		text?: string;
		externalOpen?: boolean;
	}>();

	const planTag = computed(() => {
		return props.text ?? (customAppConfig.value as any)?.planName?.name ?? 'FREE';
	});

	const emit = defineEmits<{
		(e: 'open-project-on-settings'): void;
	}>();

	const isFreePlan = computed(() => {
		return planTag.value === 'FREE';
	});

	const tagStyle = computed(() => {
		return {
			backgroundColor:
				planTag.value === 'FREE' ? 'var(--grey-100)' : 'var(--violet-100)'
		};
	});

	function openUpgradePlan() {
		if (props.externalOpen) {
			emit('open-project-on-settings');
		} else {
			goToUpgradePlan();
		}
	}
</script>

<style lang="postcss" scoped>
	.plan-tag {
		padding: 2px 8px 2px 8px;
		border-radius: 8px;
		background: rgba(236, 233, 254, 1);
		> span {
			font-size: 12px;
			font-weight: 500;
			color: var(--violet-600);
		}
		&--as-btn {
			cursor: pointer;
			transition: background 0.3s ease;
			&:hover {
				background: rgba(236, 233, 254, 0.8) !important;
			}
		}
	}
</style>
