/* eslint-disable no-console */
import {
	define<PERSON><PERSON><PERSON><PERSON><PERSON>,
	create<PERSON><PERSON>r,
	readBody,
	setResponseHeaders
} from 'h3';
import { GoogleGenAI, Type } from '@google/genai';

import axios from 'axios';
import { maskKey } from '~/server/utils/ai';
import { GLOBALS } from '~/helpers/contants/global_constants';
import { formatConnectors } from '~/composables/useMitraAPI';
// ==== BEGIN: Robust error handling + retry helpers (Gemini API) ====
const RETRYABLE_HTTP = new Set([429, 500, 503, 504]);

function clamp(num: number, min: number, max: number) {
	return Math.max(min, Math.min(max, num));
}

// Safe parser for Google-style error payloads that sometimes nest JSON as a string
function safeParseGeminiError(err: any): {
	code?: number;
	status?: string;
	message: string;
	retryAfterMs?: number;
} {
	let code: number | undefined;
	let status: string | undefined;
	let message = (err?.message || err?.statusText || 'Unknown error').toString();
	let retryAfterMs: number | undefined;

	// Try to parse nested { error: { code, status, message } } shapes embedded as string
	const raw = err?.error?.message || err?.message || err?.body || err?.data;
	if (typeof raw === 'string') {
		try {
			const parsed = JSON.parse(raw);
			const inner = parsed?.error || parsed;
			if (inner) {
				code = Number(inner.code) || code;
				status = (inner.status || inner.reason || status)?.toString();
				message = (inner.message || message).toString();
			}
		} catch (_) {
			// best-effort: some SDKs put plain text here; leave as-is
		}
	} else if (typeof raw === 'object' && raw) {
		const inner = raw.error || raw;
		code = Number(inner?.code) || code;
		status = (inner?.status || inner?.reason || status)?.toString();
		message = (inner?.message || message).toString();
	}

	// Honor Retry-After header if the SDK surfaces it
	try {
		const retryAfter =
			err?.headers?.['retry-after'] || err?.response?.headers?.get?.('retry-after');
		if (retryAfter) {
			const secs = Number(retryAfter);
			if (!Number.isNaN(secs) && secs > 0) retryAfterMs = secs * 1000;
		}
	} catch {}

	// Fallback code from statusCode/status
	code = Number(err?.status || err?.statusCode || code) || undefined;
	status = (status || err?.name || err?.statusText)?.toString();

	return { code, status, message, retryAfterMs };
}

function isRetryableStatus(code?: number) {
	return code ? RETRYABLE_HTTP.has(code) : false;
}

function mapGeminiError(err: any) {
	const { code, status, message, retryAfterMs } = safeParseGeminiError(err);
	// Map to consistent Nuxt/H3 error response, aligned with Gemini troubleshooting docs
	const statusCode = code || 500;
	const statusLabel =
		status ||
		(statusCode === 429
			? 'RESOURCE_EXHAUSTED'
			: statusCode === 503
			? 'UNAVAILABLE'
			: statusCode === 504
			? 'DEADLINE_EXCEEDED'
			: statusCode === 400
			? 'INVALID_ARGUMENT'
			: statusCode === 403
			? 'PERMISSION_DENIED'
			: statusCode === 404
			? 'NOT_FOUND'
			: 'INTERNAL');

	const retryable = isRetryableStatus(statusCode);

	return {
		statusCode,
		statusMessage: `[GEMINI_ERROR:${statusLabel}] ${message}`,
		data: {
			isGeminiError: true,
			geminiStatus: statusLabel,
			code: statusCode,
			retryable,
			retryAfterMs
		}
	};
}

function sleep(ms: number) {
	// eslint-disable-next-line promise/param-names
	return new Promise((r) => setTimeout(r, ms));
}

async function withRetry<T>(
	fn: () => Promise<T>,
	opts?: {
		attempts?: number;
		baseDelayMs?: number;
		maxDelayMs?: number;
		label?: string;
	}
) {
	const attempts = opts?.attempts ?? 3;
	const base = opts?.baseDelayMs ?? 400;
	const maxDelay = opts?.maxDelayMs ?? 8000;
	const label = opts?.label ?? 'gemini-call';

	let lastErr: any;
	for (let i = 1; i <= attempts; i++) {
		try {
			return await fn();
		} catch (err: any) {
			lastErr = err;
			const { code } = safeParseGeminiError(err);
			const retryable = isRetryableStatus(code);
			if (!retryable || i === attempts) break;

			// exponential backoff with jitter, honor Retry-After when present
			const { retryAfterMs } = safeParseGeminiError(err);
			const backoff =
				retryAfterMs ??
				Math.min(maxDelay, Math.floor(base * Math.pow(2, i - 1) + Math.random() * 200));
			console.warn(
				`⏳ ${label} retry ${i}/${attempts - 1} after ${backoff}ms (code=${code})`
			);
			await sleep(backoff);
		}
	}
	throw lastErr;
}

function suggestModel(model: string) {
	const aliases: Record<string, string> = {
		'gemini-2.5-flashss': 'gemini-2.5-flash',
		'gemini-2.5-flashs': 'gemini-2.5-flash',
		'gemini-pro': 'gemini-1.5-pro'
	};
	return aliases[model] || null;
}

function validateModelAndParams(model: string, temperature?: number) {
	if (!model || typeof model !== 'string') {
		throw createError({
			statusCode: 400,
			statusMessage:
				'[GEMINI_ERROR:INVALID_ARGUMENT] Parâmetro "model" ausente ou inválido.'
		});
	}
	const suggestion = suggestModel(model);
	if (suggestion) {
		throw createError({
			statusCode: 404,
			statusMessage: `[GEMINI_ERROR:NOT_FOUND] Modelo "${model}" não existe. Você quis dizer "${suggestion}"?`
		});
	}
	if (/\bgemini-pro\b/i.test(model)) {
		throw createError({
			statusCode: 404,
			statusMessage:
				'[GEMINI_ERROR:NOT_FOUND] Modelo "gemini-pro" não existe. Use "gemini-1.5-pro" ou outro válido.'
		});
	}
	if (typeof temperature === 'number') {
		const clamped = clamp(temperature, 0, 1);
		if (clamped !== temperature) {
			console.warn(
				`⚠️ Temperatura fora do intervalo [0,1]. Ajustando de ${temperature} para ${clamped}.`
			);
		}
	}
}

function getScreenInstructions(
	selectedScreenId: number,
	contextScreenId: string | null,
	modalScreenId: string | null,
	ALL_SCREENS: any[]
) {
	try {
		const principalScreen = ALL_SCREENS.find(
			(screen: any) => screen.id === selectedScreenId
		);

		const contextScreen = ALL_SCREENS.find(
			(screen: any) => `${screen.id}` === `${contextScreenId}`
		);

		const modalScreen = ALL_SCREENS.find(
			(screen: any) => `${screen.id}` === `${modalScreenId}`
		);
		const principalScreenTerm = `${principalScreen.name} - ID: ${principalScreen.id}`;
		const modalScreenTerm = modalScreen
			? `${modalScreen.name} - ID: ${modalScreen.id}`
			: null;

		if (!contextScreenId && !modalScreenId && selectedScreenId) {
			return `Saiba que o usuário está na tela chamada: ${principalScreenTerm} e não tem nenhuma outra tela ou modal aberto dentro dela!`;
		}

		if (modalScreenId && modalScreen) {
			const contextScreenTerm = `${contextScreen.name} - ID: ${contextScreen.id}`;
			return `Saiba que o usuário está na tela chamada: ${principalScreenTerm} e dentro dessa tela tem um menu que esta aberto outra tela chamada: ${contextScreenTerm} e dentro dessa tela ele abriu um modal com a tela ${modalScreenTerm}`;
		}

		if (contextScreenId && contextScreen) {
			const contextScreenTerm = `${contextScreen.name} - ID: ${contextScreen.id}`;
			return `Saiba que o usuário está na tela chamada: ${principalScreenTerm} e dentro dessa tela tem um menu que esta aberto outra tela chamada: ${contextScreenTerm}`;
		} else {
			return `Saiba que o usuário está na tela chamada : ${principalScreenTerm}, saiba que ela não tem nenhuma outra tela nem modal aberto dentro dela!`;
		}
	} catch (e) {
		console.log('Erro ao montar instruções da tela:', e);
	}
}

// Emit structured SSE error chunk (to be consumed by the frontend)
function emitSSEError(res: any, err: any) {
	const mapped = mapGeminiError(err);
	const payload = {
		type: 'error',
		error: {
			message: mapped.statusMessage,
			code: mapped.data?.code,
			status: mapped.data?.geminiStatus,
			retryable: mapped.data?.retryable,
			retryAfterMs: mapped.data?.retryAfterMs,
			isGeminiError: true
		},
		timestamp: Date.now()
	};
	try {
		res.write(`data: ${JSON.stringify(payload)}\n\n`);
		if (mapped.data?.retryAfterMs) {
			try {
				res.setHeader(
					'Retry-After',
					String(Math.ceil(mapped.data.retryAfterMs / 1000))
				);
			} catch {}
		}
	} catch (writeErr) {
		console.error('❌ Falha ao emitir SSE error:', writeErr);
	}
}

interface GeminiChatPayload {
	systemInstruction: string;
	userPrompt: Array<{
		role: string;
		parts: { text: string }[];
	}>;
	// customInstructions: string;
	model: string;
	googleSearchGrounding?: boolean;
	codeExecutionEnabled?: boolean;
	deepThinking?: boolean;
	files?: any[];
	// Parâmetros para integração com banco
	jdbcConfigId?: number;
	baseUrl?: string;
	nuxtBaseUrl?: string;
	nuxtToken?: string;
	isToUseUserIaCredit?: boolean;
	isToUseOwnKey?: boolean;
	selectedWorkspaceId: number;
	authorizationToken?: string;
	workspaceAiFlow?: boolean;
	projectName: string;
	selectedProjectAcessType: string;
	analyzeAllScreens?: boolean;
	isSankhya?: boolean;
	requestOrigin?: string;
	username?: string;
	mitraLogData: {
		projectId?: number;
		messageId?: string;
		sessionId?: string;
		inputValue?: string;
		isRetryMessage?: boolean;
		retryAttempt?: number;
		previousMaskedApiKey?: string | null;
	};
	useOpenRouter?: boolean;
	configChain?: {
		isMobileView: boolean;
		selectedAIMode: string;
		isFreeDb: boolean;
		isValidacao: boolean;
		isPreview: boolean;
		contextScreenId: string | null;
		modalScreenId?: string | null;
		isPlayground: boolean;
		variables: {
			today: string;
			customInstructions: string;
			dbDriverType: string;
		};
		currentUserToolSchema?: any;
		allowLilaToExecuteActions?: boolean;
		guidelinesToolsActions?: string;
		isFinalUserChatSideBar?: boolean;
		freeIaCreditsUsage: number;
		selectedScreenId: number;
	};
}

// Usar o tipo original do Gemini para compatibilidade
type StreamChunk = any;

// Função para montar server side o customInstructions
function getWorkspaceAiFlowCustomInstructions({
	files,
	sessionId,
	isSankhya
}: {
	files: any[];
	sessionId: string;
	isSankhya?: boolean;
}) {
	const CHAT_FILES = files?.map((file: any) => {
		return {
			path: `/ai-files/chat/${sessionId}/${file.name}`,
			isLoadable: false,
			isPublic: false,
			name: file.name,
			url: null,
			threadId: sessionId
		};
	});
	return `<telaDoUsuário>
		O usuário não está em nenhuma tela do mitra no momento
		</telaDoUsuário>

		<dadosParaRag>
		AMBIENTE: ${isSankhya ? 'Sankhya' : 'Mitra'}.
		NOME_PROJETO: Projeto Novo.
		ALL_SCREENS:[].
		DICIONARIO:[{"tableName":"INT_USER","descrColumnName":"DESCR","columns":[{"name":"ID","dataType":"INT","inPrimaryKey":true,"autoIncrement":false,"required":true},{"name":"DESCR","dataType":"TEXT","inPrimaryKey":false,"autoIncrement":false,"required":false},{"name":"ISDEV","dataType":"INT","inPrimaryKey":false,"autoIncrement":false,"required":false}],"singlePrimaryKeyColumnName":"ID","maxTableId":6,"jdbcConnectionConfigId":1}].
		DICIONARIO_OUTROS: [].
		CONNECTORS:[{"jdbcConfigId":1,"databaseType":"MySQL"}].
		ARQUIVOS_DRIVE:[].
		IMPORTS:[].
		ALL_FORMS:[].
		ALL_DBACTIONS:[].
		ALL_DETAILS:[].
		ALL_VARIABLES:[{"id":-9999,"name":":VAR_USER","canNull":false,"content":"5","fillConfigs":[],"bySessionId":false,"native":true},{"id":-9998,"name":":VAR_SESSION","canNull":false,"content":"","fillConfigs":[],"bySessionId":false,"native":true},{"id":-9997,"name":":VAR_TOKEN","canNull":false,"content":"","fillConfigs":[],"bySessionId":false,"native":true},{"id":-9996,"name":":VAR_URL","canNull":false,"content":"","fillConfigs":[],"bySessionId":false,"native":true}].
		ALL_ACTIONS:[].
		NATIVE_SERVER_ENDPOINT:{"apiUrl":""}.
		NATIVE_BEARER_ROOT_TOKEN:{"token":""}.
		CHAT_FILES:${JSON.stringify(CHAT_FILES)}.
		</dadosParaRag>`;
}
async function getCustomInstructions({
	selectedAiMode,
	selectedScreenId,
	// jdbcConfigId,
	baseUrl,
	payloadUserToken,
	isFinalUserChatSideBar,
	selectedProjectAcessType,
	contextScreenId,
	modalScreenId,
	analyzeAllScreens,
	isPlayground,
	isSankhya,
	sessionId,
	projectName
}: {
	selectedAiMode: string;
	selectedScreenId: number;
	jdbcConfigId: number;
	baseUrl: string;
	payloadUserToken: string;
	isFinalUserChatSideBar?: boolean;
	selectedProjectAcessType: string;
	analyzeAllScreens: boolean;
	isPlayground: boolean;
	isSankhya?: boolean;
	sessionId: string;
	projectName: string;
	contextScreenId: string | null;
	modalScreenId?: string | null;
}): Promise<string> {
	let DICIONARIO,
		ALL_SCREENS,
		ALL_FORMS,
		ALL_DBACTIONS,
		ALL_VARIABLES,
		ALL_ACTIONS,
		PUBLIC_FILES,
		CHAT_FILES,
		LOADABLE_FILES,
		ALL_DETAILS,
		NATIVE_SERVER_ENDPOINT,
		NATIVE_BEARER_ROOT_TOKEN,
		CONNECTORS,
		DRIVE,
		ONLINE_TABLES,
		IMPORTS;

	const mitraAPI = createMitraAPI(baseUrl, payloadUserToken);

	if (selectedAiMode === 'AI_ANALYST') {
		const isBusiness = selectedProjectAcessType === 'BUSINESS';

		const [
			dicionario,
			allScreensOrActions,
			OnlineTables,
			connectors,
			allVariables
		] = await Promise.all([
			mitraAPI.fetchDictionary(),
			mitraAPI.requestByScreenContext({
				screenId: selectedScreenId,
				analyzeAllScreens: !(isBusiness || isFinalUserChatSideBar),
				isPlayground
			}),
			mitraAPI.fetchOnlineTables(),
			mitraAPI.fetchConnectors({ isSankhya }),
			mitraAPI.fetchVariables()
		]);

		ALL_SCREENS = allScreensOrActions;
		DICIONARIO = dicionario;
		ALL_VARIABLES = allVariables;
		CONNECTORS = connectors;
		ONLINE_TABLES = OnlineTables;

		// Set defaults for unused data to avoid undefined errors
		ALL_ACTIONS = [];
		ALL_FORMS = [];
		ALL_DBACTIONS = [];
		ALL_DETAILS = [];
		NATIVE_SERVER_ENDPOINT = null;
		NATIVE_BEARER_ROOT_TOKEN = null;
		PUBLIC_FILES = [];
		CHAT_FILES = [];
		LOADABLE_FILES = [];
		IMPORTS = [];
		DRIVE = [];

		return getAnalystCustomInstructions(
			selectedScreenId,
			ALL_SCREENS,
			DICIONARIO,
			ONLINE_TABLES,
			CONNECTORS,
			ALL_VARIABLES,
			contextScreenId,
			modalScreenId
		);
	} else {
		// AI Builder and other modes - fetch all required data
		const [
			dicionario,
			allScreens,
			allForms,
			allDbActions,
			allVariables,
			allActions,
			allDetails,
			nativeServerEndpoint,
			nativeBearerRootToken,
			connectors,
			privateFiles,
			publicFiles,
			imports,
			OnlineTables,
			drives
		] = await Promise.all([
			mitraAPI.fetchDictionary(),
			mitraAPI.requestByScreenContext({
				screenId: selectedScreenId,
				analyzeAllScreens,
				isPlayground
			}),
			mitraAPI.fetchForms(),
			mitraAPI.fetchDbActions(),
			mitraAPI.fetchVariables(),
			mitraAPI.fetchActions(),
			mitraAPI.fetchDetails(),
			mitraAPI.getNativeServerEndpoint(),
			mitraAPI.getNativeBearerRootToken(),
			mitraAPI.fetchConnectors({ isSankhya }),
			mitraAPI.getChatFiles(sessionId),
			mitraAPI.getPublicFiles(sessionId),
			mitraAPI.fetchImports(),
			mitraAPI.fetchOnlineTables(),
			mitraAPI.fetchDrive()
		]);

		DICIONARIO = dicionario;
		IMPORTS = imports;
		ONLINE_TABLES = OnlineTables;
		DRIVE = drives;

		ALL_SCREENS = allScreens;
		ALL_FORMS = allForms;
		ALL_DBACTIONS = allDbActions;
		ALL_VARIABLES = allVariables;
		ALL_ACTIONS = allActions;
		ALL_DETAILS = allDetails;
		CHAT_FILES = privateFiles;
		PUBLIC_FILES = publicFiles?.filter((f: any) => f.isPublic) ?? [];
		LOADABLE_FILES = publicFiles?.filter((f: any) => f.isLoadable) ?? [];
		NATIVE_SERVER_ENDPOINT = nativeServerEndpoint;
		NATIVE_BEARER_ROOT_TOKEN = nativeBearerRootToken;
		CONNECTORS = connectors;

		return getBuilderCustomInstructions(
			projectName,
			selectedScreenId,
			ALL_SCREENS,
			DICIONARIO,
			CONNECTORS,
			ALL_FORMS,
			ALL_DBACTIONS,
			ALL_DETAILS,
			ALL_VARIABLES,
			ALL_ACTIONS,
			PUBLIC_FILES,
			CHAT_FILES,
			LOADABLE_FILES,
			NATIVE_SERVER_ENDPOINT,
			NATIVE_BEARER_ROOT_TOKEN,
			ONLINE_TABLES,
			IMPORTS,
			DRIVE,
			contextScreenId,
			modalScreenId,
			isSankhya
		);
	}
}

function getBuilderCustomInstructions(
	projectName: string,
	selectedScreenId: number,
	ALL_SCREENS: any,
	DICIONARIO: any,
	CONNECTORS: any,
	ALL_FORMS: any,
	ALL_DBACTIONS: any,
	ALL_DETAILS: any,
	ALL_VARIABLES: any,
	ALL_ACTIONS: any,
	PUBLIC_FILES: any,
	CHAT_FILES: any,
	LOADABLE_FILES: any,
	NATIVE_SERVER_ENDPOINT: any,
	NATIVE_BEARER_ROOT_TOKEN: any,
	ONLINE_TABLES: any,
	IMPORTS: any,
	DRIVE: any,
	contextScreenId: string | null,
	modalScreenId: string | null = null,
	isSankhya?: boolean
): string {
	let screenTerms = 'O usuário não está em nenhuma tela do mitra no momento';
	if (ALL_SCREENS.find((screen: any) => screen.id === selectedScreenId)?.name) {
		screenTerms = getScreenInstructions(
			selectedScreenId,
			contextScreenId,
			modalScreenId,
			ALL_SCREENS
		) as string;
	}
	console.log('>>>>>> INSTRUÇÕES DA TELA: ', screenTerms);
	return `
						<telaDoUsuário>
						${screenTerms}
						</telaDoUsuário>

						<dadosParaRag>
						AMBIENTE: ${isSankhya ? 'Sankhya' : 'Mitra'}.
						NOME_PROJETO: ${projectName}.
						ALL_SCREENS:${JSON.stringify(ALL_SCREENS)}.
						DICIONARIO:${JSON.stringify(DICIONARIO)}.
						DICIONARIO_OUTROS:${JSON.stringify(ONLINE_TABLES)}.
						CONNECTORS:${JSON.stringify(CONNECTORS)}.
						ARQUIVOS_DRIVE:${JSON.stringify(DRIVE)}.
						IMPORTS:${JSON.stringify(IMPORTS)}.
						ALL_FORMS:${JSON.stringify(ALL_FORMS)}.
						ALL_DBACTIONS:${JSON.stringify(ALL_DBACTIONS)}.
						ALL_DETAILS:${JSON.stringify(ALL_DETAILS)}.
						ALL_VARIABLES:${JSON.stringify(ALL_VARIABLES)}.
						ALL_ACTIONS:${JSON.stringify(ALL_ACTIONS)}.
						PUBLIC_FILES:${JSON.stringify(PUBLIC_FILES)}.
						CHAT_FILES:${JSON.stringify(CHAT_FILES)}.
						LOADABLE_FILES:${JSON.stringify(LOADABLE_FILES)}.
						NATIVE_SERVER_ENDPOINT:${JSON.stringify(NATIVE_SERVER_ENDPOINT)}.
						NATIVE_BEARER_ROOT_TOKEN:${JSON.stringify(NATIVE_BEARER_ROOT_TOKEN)}.
						</dadosParaRag>
						`;
}
function getAnalystCustomInstructions(
	selectedScreenId: number,
	ALL_SCREENS: any,
	DICIONARIO: any,
	ONLINE_TABLES: any,
	CONNECTORS: any,
	ALL_VARIABLES: any,
	contextScreenId: string | null,
	modalScreenId: string | null = null
): string {
	let screenTerms = 'O usuário não está em nenhuma tela do mitra no momento';
	if (ALL_SCREENS.find((screen: any) => screen.id === selectedScreenId)?.name) {
		screenTerms = getScreenInstructions(
			selectedScreenId,
			contextScreenId,
			modalScreenId,
			ALL_SCREENS
		) as string;
	}
	console.log('>>>>>> INSTRUÇÕES DA TELA: ', screenTerms);
	return `
						<telaDoUsuário>
						${screenTerms}
						</telaDoUsuário>

						<dadosParaRag>
						ALL_SCREENS:${JSON.stringify(ALL_SCREENS)}.
						DICIONARIO:${JSON.stringify(DICIONARIO)}.
						DICIONARIO_OUTROS:${JSON.stringify(ONLINE_TABLES)}.
						CONNECTORS:${JSON.stringify(CONNECTORS)}.
						ALL_VARIABLES:${JSON.stringify(ALL_VARIABLES)}.
						</dadosParaRag>
						`;
}

function createMitraAPI(baseURL: string, token: string) {
	const axiosInstance = axios.create({
		baseURL,
		headers: {
			Accept: 'application/json',
			Authorization: token,
			'Content-Type': 'application/json'
		}
	});

	// Adicionar interceptor para logar cURL em caso de erro em qualquer request
	axiosInstance.interceptors.response.use(
		(response) => response,
		(error) => {
			if (error.isAxiosError && error.config) {
				const config = error.config;
				const headers = Object.entries(config.headers)
					.map(([key, value]) => `-H '${key}: ${String(value).replace(/'/g, "'\\''")}'`)
					.join(' ');

				let fullUrl = `${config.baseURL || ''}${config.url || ''}`;
				if (config.params) {
					const queryString = new URLSearchParams(config.params).toString();
					if (queryString) {
						fullUrl += `?${queryString}`;
					}
				}

				const curlCommand = `curl -X ${config.method.toUpperCase()} '${fullUrl}' ${headers}`;
				const logBlock = `
/**********************************************************************************/
/*************************** 🐚 cURL PARA DEPURAÇÃO 🐚 ****************************/
/**********************************************************************************/
// ERRO NA CHAMADA: ${config.method.toUpperCase()} ${fullUrl}
// Copie o comando abaixo para depurar no seu terminal ou Postman:

${curlCommand}

/**********************************************************************************/
`;
				console.error(logBlock);
			}
			return Promise.reject(error);
		}
	);

	return {
		async fetchDictionary(jdbcConnectionConfigId?: number) {
			try {
				const params = jdbcConnectionConfigId
					? `?jdbcConnectionConfigId=${jdbcConnectionConfigId}`
					: '';
				const response = await axiosInstance.get(`/iaShortcuts/dictionary${params}`);

				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Dictionary', error);
				throw new Error('Erro ao buscar dados do MITRA: Dictionary');
			}
		},

		async fetchScreens({
			withComponents,
			isPlayground
		}: { withComponents?: boolean; isPlayground?: boolean } = {}) {
			try {
				const params: any = {};
				if (withComponents) params.withComponents = true;
				if (isPlayground) params.iaPlayground = true;

				const response = await axiosInstance.get('/iaShortcuts/screen/all', {
					params
				});

				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Screens (all)', error);
				throw new Error('Erro ao buscar dados do MITRA: Screens (all)');
			}
		},

		async fetchOnlySelectedScreenWithComponent(id: number, isPlayground: boolean) {
			try {
				const playgroundParam = isPlayground ? { iaPlayground: true } : {};

				const [selectedResponse, allResponse] = await Promise.all([
					axiosInstance.get(`/iaShortcuts/screen/${id}`, {
						params: playgroundParam
					}),
					axiosInstance.get('/iaShortcuts/screen/all', {
						params: {
							withComponents: false,
							...playgroundParam
						}
					})
				]);

				const selectedScreen = selectedResponse.data;
				const allScreens = allResponse.data;
				const allWithoutSelected = allScreens.filter((screen: any) => screen.id !== id);

				return [selectedScreen, allWithoutSelected];
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Screens (selected)', error);
				throw new Error('Erro ao buscar dados do MITRA: Screens (selected)');
			}
		},

		async requestByScreenContext({
			screenId,
			analyzeAllScreens,
			isPlayground
		}: {
			screenId: number;
			analyzeAllScreens: boolean;
			isPlayground: boolean;
		}) {
			try {
				if (screenId < 0 && !analyzeAllScreens) {
					return [];
				}

				if (!screenId || analyzeAllScreens) {
					return await this.fetchScreens({
						withComponents: analyzeAllScreens,
						isPlayground
					});
				} else {
					return await this.fetchOnlySelectedScreenWithComponent(screenId, isPlayground);
				}
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: ScreenContext', error);
				throw new Error('Erro ao buscar dados do MITRA: ScreenContext');
			}
		},

		async fetchImports() {
			try {
				const response = await axiosInstance.get('/dataLoader');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Imports', error);
				throw new Error('Erro ao buscar dados do MITRA: Imports');
			}
		},

		async fetchOnlineTables() {
			try {
				const response = await axiosInstance.get('/queryAlias');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: OnlineTables', error);
				throw new Error('Erro ao buscar dados do MITRA: OnlineTables');
			}
		},

		async fetchDrive() {
			try {
				const response = await axiosInstance.get('/iaShortcuts/file');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Drive', error);
				throw new Error('Erro ao buscar dados do MITRA: Drive');
			}
		},

		async fetchForms() {
			try {
				const response = await axiosInstance.get('/iaShortcuts/form');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Forms', error);
				throw new Error('Erro ao buscar dados do MITRA: Forms');
			}
		},

		async fetchDbActions() {
			try {
				const response = await axiosInstance.get('/dmlAction');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: DBActions', error);
				throw new Error('Erro ao buscar dados do MITRA: DBActions');
			}
		},

		async fetchVariables() {
			try {
				const response = await axiosInstance.get('/iaShortcuts/customVariable');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Variables', error);
				throw new Error('Erro ao buscar dados do MITRA: Variables');
			}
		},

		async fetchActions() {
			try {
				const response = await axiosInstance.get('/iaShortcuts/action/all');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Actions', error);
				throw new Error('Erro ao buscar dados do MITRA: Actions');
			}
		},

		async fetchDetails() {
			try {
				const response = await axiosInstance.get('/iaShortcuts/detailsModal/all');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Details', error);
				throw new Error('Erro ao buscar dados do MITRA: Details');
			}
		},

		async getNativeServerEndpoint() {
			try {
				const response = await axiosInstance.get('/iaShortcuts/url');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: NativeServerEndpoint', error);
				throw new Error('Erro ao buscar dados do MITRA: NativeServerEndpoint');
			}
		},

		async getNativeBearerRootToken() {
			try {
				const response = await axiosInstance.get('/iaShortcuts/token');
				return response.data;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: NativeBearerRootToken', error);
				throw new Error('Erro ao buscar dados do MITRA: NativeBearerRootToken');
			}
		},

		async fetchConnectors({ isSankhya }: { isSankhya?: boolean } = {}) {
			try {
				// Definido localmente para manter compatibilidade com o client-side
				const response = await axiosInstance.get('/conector');
				const resp = response.data;

				const CONNECTORS = formatConnectors(resp, { isSankhya });

				return CONNECTORS;
			} catch (error) {
				console.error('❌ Erro ao buscar dados do MITRA: Connector', error);
				throw new Error('Erro ao buscar dados do MITRA: Connector');
			}
		},

		async getChatFiles(threadId: string) {
			try {
				const response = await axiosInstance.get(`/iaShortcuts/chatFile/${threadId}`);
				return response.data?.files
					? response.data.files.map((file: any) => ({ ...file, threadId }))
					: [];
			} catch (error) {
				console.error('❌ Erro ao buscar imagens:', error);
				throw new Error('Erro ao buscar imagens');
			}
		},

		async getPublicFiles(threadId: string) {
			try {
				const response = await axiosInstance.get(`/iaShortcuts/file`);
				return response.data?.files
					? response.data.files.map((file: any) => ({ ...file, threadId }))
					: [];
			} catch (error) {
				console.error('❌ Erro ao buscar imagens:', error);
				throw new Error('Erro ao buscar imagens');
			}
		}
	};
}

// Função para obter API key do OpenRouter
function getOpenRouterApiKey(
	_requestUrl: string,
	_userToken: string,
	_isToUseUserIaCredit: boolean,
	_isToUseOwnKey: boolean,
	_workspaceId: number,
	_freeIaCreditsUsage: number,
	_isSankhya: boolean
): string {
	console.log('🔑 Obtendo API key do OpenRouter');

	// Chave hardcoded do OpenRouter
	const HARDCODED_OPENROUTER_KEY =
		'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';

	console.log('✅ Usando chave OpenRouter hardcoded');
	return HARDCODED_OPENROUTER_KEY;

	// Código comentado para buscar do sistema (pode ser usado no futuro)
	/*
	const mitraEcpAxios = axios.create({
		baseURL: requestUrl,
		headers: {
			Accept: 'application/json',
			Authorization: userToken
		}
	});

	try {
		const userIaConfigResponse = await mitraEcpAxios.get('/mitraspace/user/iaConfig');
		const data = userIaConfigResponse.data;

		// Buscar por modelo OpenRouter (ID 12 ou similar)
		const openRouterModel = data.model?.find((m: any) => m.id === 12 || m.name?.toLowerCase().includes('openrouter'));

		if (openRouterModel?.modelKey) {
			console.log('✅ OpenRouter API key obtida com sucesso');
			return openRouterModel.modelKey;
		}

		// Fallback: tentar buscar por workspace
		try {
			const workspaceResponse = await mitraEcpAxios.get(`/mitraspace/iaConfig/${workspaceId}/12`);
			const modelKey = workspaceResponse.data?.openaiAccessKey;

			if (modelKey) {
				console.log('✅ OpenRouter API key obtida via workspace');
				return modelKey;
			}
		} catch (error) {
			console.warn('⚠️ Não foi possível obter OpenRouter key via workspace');
		}

		throw new Error('❌ OpenRouter API key não encontrada');
	} catch (error) {
		console.error('❌ Erro ao buscar OpenRouter API key:', error);
		throw error;
	}
	*/
}

// Função para retornar os componentes customizados - curl --location 'https://prod2.mitrasheet.com:8080/rest/v0/Custom Components' --header 'Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDaGFpbiBBdHVhbCIsIlgtVGVuYW50SUQiOiJ0ZW5hbnRfMjU5ODYifQ.I6kI6lVdTm3YV7bRQsR9cNiMXr79SHftlzdvDZRpSaoZD-WnOwHdOI8d0m5g_vzBHsdvvBu0FuT2izwkVudvog'
async function getCustomComponents() {
	try {
		const response = await axios.get(
			'https://prod2.mitrasheet.com:8080/rest/v0/Custom Components',
			{
				headers: {
					Authorization:
						'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDaGFpbiBBdHVhbCIsIlgtVGVuYW50SUQiOiJ0ZW5hbnRfMjU5ODYifQ.I6kI6lVdTm3YV7bRQsR9cNiMXr79SHftlzdvDZRpSaoZD-WnOwHdOI8d0m5g_vzBHsdvvBu0FuT2izwkVudvog'
				}
			}
		);
		return response.data;
	} catch (error) {
		console.error('❌ Erro ao buscar Custom Components:', error);
		throw error;
	}
}

// Função para buscar CHAIN do Mitra API
async function getChainFromMitraAPI(configChain: any): Promise<string> {
	// BASEURL e Authorization hardcoded de um projeto existente no Mitra em https://app.mitralab.io/w/9300/p/14458/
	const mitraAxios = axios.create({
		baseURL: 'https://prod2.mitrasheet.com:8080/rest/v0',
		headers: {
			'Content-Type': 'application/json',
			Authorization: GLOBALS.API_CHAIN_TK
		}
	});

	let chainEndpoint = '';

	if (configChain.isMobileView) {
		chainEndpoint = '/MOBILE_MAPPED_QA';
	} else if (configChain.selectedAIMode === 'AI_ANALYST') {
		chainEndpoint = configChain.isFreeDb ? '/ANALYST_FDB_QA' : '/ANALYST_MBD_QA';
	} else if (configChain.isFreeDb) {
		chainEndpoint = configChain.isValidacao ? '/FREEDB_QA_TEMP' : '/FREEDB_QA';
	} else {
		chainEndpoint = '/MAPPED_QA';
	}

	console.log(`🔗 CHAIN DEBUG - Buscando CHAIN do endpoint: ${chainEndpoint}`);

	try {
		const response = await mitraAxios.get(chainEndpoint);
		let chain = response.data;

		// Garantir que chain é uma string
		if (typeof chain !== 'string') {
			console.log(
				'🔗 CHAIN DEBUG - Resposta da API não é string, convertendo:',
				typeof chain
			);
			chain = JSON.stringify(chain);
		}

		const customComponents = await getCustomComponents();

		// Garantir que customComponents tem o conteúdo esperado
		if (!customComponents?.content || customComponents.content.length === 0) {
			console.error(
				'❌ Custom Components não tem a estrutura esperada:',
				customComponents
			);
			// throw new Error('Custom Components inválidos');
		}

		// Fazer substituições dinâmicas para todos os componentes
		let chainReplaced = chain;

		// Primeiro, tratar o caso especial $others
		const othersRegex = /\$others\.(\w+)/g;
		chainReplaced = chainReplaced.replace(
			othersRegex,
			(match: string, property: string) => {
				// Filtrar componentes que têm others = 1
				const othersComponents = customComponents.content.filter(
					(comp: any) => comp.others === 1
				);

				if (othersComponents.length === 0) {
					console.warn(`⚠️ Nenhum componente com others=1 encontrado para ${match}`);
					return match;
				}

				// Evitar loop infinito - não permitir acessar a propriedade 'others'
				if (property === 'others') {
					console.warn(`⚠️ Propriedade 'others' não permitida para evitar loop infinito`);
					return match;
				}

				// Concatenar os valores da propriedade de todos os componentes others
				const values = othersComponents
					.map((comp: any) => {
						if (
							property in comp &&
							comp[property] !== undefined &&
							comp[property] !== null
						) {
							return comp[property];
						}
						return null;
					})
					.filter((value: any) => value !== null);

				if (values.length > 0) {
					const concatenatedValue = values.join('\n\n--------------------\n\n');
					// console.log(
					// 	`🔄 Substituindo ${match} por ${values.length} componentes others concatenados`
					// );
					return concatenatedValue;
				}

				console.warn(
					`⚠️ Propriedade '${property}' não encontrada em nenhum componente others`
				);
				return match;
			}
		);

		// Depois, tratar os componentes individuais normalmente
		customComponents.content.forEach((component: any) => {
			const description = component['Descrição'];

			if (description) {
				// Criar regex para encontrar todos os padrões $DESCRIPTION.PROPERTY
				const descriptionRegex = new RegExp(`\\$${description}\\.(\\w+)`, 'g');

				// Encontrar todas as ocorrências e fazer replace
				chainReplaced = chainReplaced.replace(
					descriptionRegex,
					(match: string, property: string) => {
						// Verificar se a propriedade existe no componente
						if (
							property in component &&
							component[property] !== undefined &&
							component[property] !== null
						) {
							// console.log(`🔄 Substituindo ${match} por:`, component[property]);
							return component[property];
						}

						// Se não encontrar a propriedade, manter o texto original
						console.warn(`⚠️ Propriedade '${property}' não encontrada em ${description}`);
						return match;
					}
				);
			}
		});

		// Atualizar a chain com o resultado da substituição
		chain = chainReplaced;

		// Aplicar transformações de variáveis
		if (configChain.variables) {
			chain = Object.entries(configChain.variables).reduce(
				(currentChain: string, [key, value]) => {
					const regex = new RegExp(`\\$\\{?${key}\\}?`, 'g');
					return currentChain.replace(regex, String(value));
				},
				chain
			);
		}

		// console.log('✅✅✅✅✅✅✅✅✅✅ antes da transformação dash:');
		// console.log(configChain.isPlayground);
		// console.log(configChain.selectedAIMode);
		// console.log(chain);
		// console.log('✅✅✅✅✅✅✅✅✅✅ fim:');

		// Aplicar transformação para playground se necessário
		if (configChain.isPlayground && configChain.selectedAIMode === 'AI_ANALYST') {
			chain = transformAddTelasInAddDash(chain);
		}
		// console.log('✅✅✅✅✅✅✅✅✅✅ nova chain:');
		// console.log(chain);
		// console.log('✅✅✅✅✅✅✅✅✅✅ fim:');

		console.log(
			`✅ CHAIN DEBUG - CHAIN obtida com sucesso (${chain.length} chars)`
		);
		return chain;
	} catch (error) {
		console.error('❌ CHAIN DEBUG - Erro ao buscar CHAIN:', error);
		throw error;
	}
}

// Função para transformar ADD_TELAS em ADD_DASHBOARD (migrada do client-side)
function transformAddTelasInAddDash(chain: string): string {
	let modifiedChain = chain;

	// Step 1: Rename top-level keys
	modifiedChain = modifiedChain.replaceAll('ADD_TELAS', 'ADD_DASHBOARD');
	modifiedChain = modifiedChain.replaceAll('"ADD_TELAS"', '"ADD_DASHBOARD"');
	modifiedChain = modifiedChain.replaceAll(
		'\\"ADD_TELAS\\"',
		'\\"ADD_DASHBOARD\\"'
	);
	modifiedChain = modifiedChain.replaceAll('ALT_TELAS', 'ALT_DASHBOARD');
	modifiedChain = modifiedChain.replaceAll('"ALT_TELAS"', '"ALT_DASHBOARD"');
	modifiedChain = modifiedChain.replaceAll(
		'\\"ALT_TELAS\\"',
		'\\"ALT_DASHBOARD\\"'
	);
	modifiedChain = modifiedChain.replaceAll(
		'"DELETE_TELAS"',
		'"DELETE_DASHBOARD"'
	);

	// Step 2a: Transform ADD_DASHBOARD objects (normal)
	const addObjectRegex =
		/\{([^}]*?"name":\s*([^,}]+)[^}]*?"height":\s*([^,}]+)[^}]*)\}/g;
	modifiedChain = modifiedChain.replace(
		/"ADD_DASHBOARD":\s*\[([^\]]*)\]/g,
		(_match: any, arrayContent: any) => {
			const transformedObjects = arrayContent.replace(
				addObjectRegex,
				(_objMatch: any, fullObj: any, nameVal: any, heightVal: any) => {
					const updated = fullObj
						.replace(/"name":\s*[^,}]+/, `"alias": ${nameVal}`)
						.replace(`"height": ${heightVal}`, `"height": ${heightVal}`);
					return `{${updated}}`;
				}
			);
			return `"ADD_DASHBOARD": [${transformedObjects}]`;
		}
	);

	// Step 2b: Transform ADD_DASHBOARD objects (escaped)
	const escapedAddObjectRegex =
		/\{([^}]*?\\"name\\":\s*([^,}]+)[^}]*?\\"height\\":\s*([^,}]+)[^}]*)\}/g;
	modifiedChain = modifiedChain.replace(
		/\\"ADD_DASHBOARD\\":\s*\[([^\]]*)\]/g,
		(_match: any, arrayContent: any) => {
			const transformedObjects = arrayContent.replace(
				escapedAddObjectRegex,
				(_objMatch: any, fullObj: any, nameVal: any, heightVal: any) => {
					const updated = fullObj
						.replace(/\\"name\\":\s*[^,}]+/, `\\"alias\\": ${nameVal}`)
						.replace(`\\"height\\": ${heightVal}`, `\\"height\\": ${heightVal}`);
					return `{${updated}}`;
				}
			);
			return `\\"ADD_DASHBOARD\\": [${transformedObjects}]`;
		}
	);

	// Step 3a: Transform ALT_DASHBOARD objects (normal)
	const altObjectRegex =
		/\{([^}]*?"id":\s*([^,}]+)[^}]*?"name":\s*([^,}]+)[^}]*?"height":\s*([^,}]+)[^}]*)\}/g;
	modifiedChain = modifiedChain.replace(
		/"ALT_DASHBOARD":\s*\[([^\]]*)\]/g,
		(_match: any, arrayContent: any) => {
			const transformedObjects = arrayContent.replace(
				altObjectRegex,
				(_objMatch: any, fullObj: any, idVal: any, nameVal: any, heightVal: any) => {
					const updated = fullObj
						.replace(/"name":\s*[^,}]+/, `"alias": ${nameVal}`)
						.replace(`"id": ${idVal}`, `"id": ${idVal}`) // manter o id
						.replace(`"height": ${heightVal}`, `"height": ${heightVal}`);
					return `{${updated}}`;
				}
			);
			return `"ALT_DASHBOARD": [${transformedObjects}]`;
		}
	);

	// Step 3b: Transform ALT_DASHBOARD objects (escaped)
	const escapedAltObjectRegex =
		/\{([^}]*?\\"id\\":\s*([^,}]+)[^}]*?\\"name\\":\s*([^,}]+)[^}]*?\\"height\\":\s*([^,}]+)[^}]*)\}/g;
	modifiedChain = modifiedChain.replace(
		/\\"ALT_DASHBOARD\\":\s*\[([^\]]*)\]/g,
		(_match: any, arrayContent: any) => {
			const transformedObjects = arrayContent.replace(
				escapedAltObjectRegex,
				(_objMatch: any, fullObj: any, idVal: any, nameVal: any, heightVal: any) => {
					const updated = fullObj
						.replace(/\\"name\\":\s*[^,}]+/, `\\"alias\\": ${nameVal}`)
						.replace(`\\"id\\": ${idVal}`, `\\"id\\": ${idVal}`)
						.replace(`\\"height\\": ${heightVal}`, `\\"height\\": ${heightVal}`);
					return `{${updated}}`;
				}
			);
			return `\\"ALT_DASHBOARD\\": [${transformedObjects}]`;
		}
	);

	return modifiedChain;
}

// Função para processar function calls do OpenRouter
async function processOpenRouterFunctionCalls(
	functionCalls: any[],
	apiKey: string,
	model: string,
	messages: any[],
	systemInstruction: string,
	res: any,
	_tools?: any[],
	payload?: any
) {
	console.log(
		'🔧 Processando function calls do OpenRouter:',
		functionCalls.length
	);

	// Executar cada function call
	const functionResponses: any[] = [];

	for (const functionCall of functionCalls) {
		if (functionCall.name === 'run_sql') {
			const args = functionCall.args as { query: string; jdbcConfigId?: number };
			const query = args?.query;
			const jdbcConfigId = args?.jdbcConfigId;

			if (query && payload) {
				try {
					// Executar SQL usando a mesma lógica do Gemini
					const finalJdbcConfigId = jdbcConfigId || payload.jdbcConfigId || 1;
					const baseUrl = payload.baseUrl || 'https://api0.mitraecp.com:1005';
					const authToken = payload.authorizationToken;

					if (!authToken) {
						throw new Error('Token de autorização não fornecido');
					}

					const response = await fetch(`${baseUrl}/iaShortcuts/query`, {
						method: 'POST',
						headers: {
							Accept: 'application/json, text/plain, */*',
							Authorization: authToken,
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({ query, jdbcConfigId: finalJdbcConfigId })
					});

					if (!response.ok) {
						const errorText = await response.text();
						throw new Error(`SQL execution failed: ${response.status} - ${errorText}`);
					}

					const result = await response.json();

					console.log('🔧 SQL executado com sucesso:', query);

					functionResponses.push({
						role: 'user',
						parts: [
							{
								functionResponse: {
									name: 'run_sql',
									response: result
								}
							}
						]
					});
				} catch (error) {
					console.error('❌ Erro ao executar SQL:', error);
					functionResponses.push({
						role: 'user',
						parts: [
							{
								functionResponse: {
									name: 'run_sql',
									response: {
										success: false,
										error: error instanceof Error ? error.message : 'Erro desconhecido'
									}
								}
							}
						]
					});
				}
			}
		}
	}

	// Fazer nova chamada ao OpenRouter com os resultados das function calls
	if (functionResponses.length > 0) {
		console.log(
			'🔄 Fazendo nova chamada ao OpenRouter com resultados das function calls'
		);

		// Adicionar as respostas das function calls como mensagens do usuário
		const newMessages = [
			...messages,
			{
				role: 'user',
				content: `Function call results: ${JSON.stringify(
					functionResponses.map((r) => r.parts[0].functionResponse)
				)}`
			}
		];

		// Fazer nova chamada recursiva sem tools para evitar loops infinitos
		await streamOpenRouterChat(
			apiKey,
			model,
			newMessages,
			systemInstruction,
			res,
			undefined,
			payload
		);
	}
}

// Função para fazer streaming com OpenRouter
async function streamOpenRouterChat(
	apiKey: string,
	model: string,
	messages: any[],
	systemInstruction: string,
	res: any,
	tools?: any[],
	payload?: any
) {
	console.log('🚀 Iniciando streaming com OpenRouter');
	console.log('📝 OpenRouter - Mensagens recebidas:', messages.length);
	console.log(
		'📝 OpenRouter - Primeira mensagem:',
		JSON.stringify(messages[0], null, 2).substring(0, 299900)
	);
	console.log(
		'📝 OpenRouter - Última mensagem:',
		JSON.stringify(messages[messages.length - 1], null, 2).substring(0, 299900)
	);

	const openRouterPayload: any = {
		model,
		messages: [
			{
				role: 'system',
				content: systemInstruction
			},
			...messages.map((msg) => {
				// Converter formato Gemini para OpenAI/OpenRouter
				if (msg.parts) {
					const content: any[] = [];

					for (const part of msg.parts) {
						if (part.text) {
							content.push({
								type: 'text',
								text: part.text
							});
						} else if (part.inlineData) {
							// OpenRouter suporta imagens no formato base64
							content.push({
								type: 'image_url',
								image_url: {
									url: `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`
								}
							});
						} else if (part.functionCall) {
							// Converter function calls do Gemini para OpenRouter
							content.push({
								type: 'text',
								text: `Function call: ${part.functionCall.name}(${JSON.stringify(
									part.functionCall.args
								)})`
							});
						} else if (part.functionResponse) {
							// Converter function responses do Gemini para OpenRouter
							content.push({
								type: 'text',
								text: `Function result: ${JSON.stringify(part.functionResponse.response)}`
							});
						}
					}

					return {
						role: msg.role === 'model' ? 'assistant' : msg.role, // Converter 'model' para 'assistant'
						content:
							content.length === 1 && content[0].type === 'text' ? content[0].text : content
					};
				}

				return {
					role: msg.role === 'model' ? 'assistant' : msg.role, // Converter 'model' para 'assistant'
					content: msg.content || ''
				};
			})
		],
		stream: true,
		temperature: 0.1,
		max_tokens: 0
	};

	// Adicionar reasoning apenas para modelos que suportam
	const supportsReasoning = model.includes('gemini') || model.includes('gpt');
	if (supportsReasoning) {
		openRouterPayload.reasoning = {
			max_tokens: -1,
			exclude: false
		};
		console.log('🧠 Reasoning habilitado para modelo:', model);
	}

	console.log(
		'📝 OpenRouter - Payload final:',
		JSON.stringify(
			{
				model: openRouterPayload.model,
				messages: openRouterPayload.messages.map((m: any) => ({
					role: m.role,
					content:
						typeof m.content === 'string'
							? m.content.substring(0, 199900)
							: '[complex content]'
				})),
				reasoning: openRouterPayload.reasoning
			},
			null,
			2
		)
	);

	// eslint-disable-next-line unicorn/escape-case
	console.log('\x1b[33mFULL PAYLOAD:', openRouterPayload, '\x1b[0m');

	// console.log('\x1B[33m=== FULL PAYLOAD BRUTO ===\x1B[0m');
	// console.log(JSON.stringify(openRouterPayload, null, 2));
	// console.log('\x1B[33m=== FIM PAYLOAD ===\x1B[0m');

	// Adicionar tools se fornecidas E se não for AI_BUILDER
	const isAIBuilder = payload?.configChain?.selectedAIMode === 'AI_BUILDER';
	if (tools && tools.length > 0 && !isAIBuilder) {
		// Converter tools do formato Gemini para OpenRouter/OpenAI
		const openRouterTools = tools.map((tool) => {
			if (tool.functionDeclarations) {
				const funcDecl = tool.functionDeclarations[0];

				// Converter schema do Gemini para OpenAI format
				const convertedFunction = {
					name: funcDecl.name,
					description: funcDecl.description,
					parameters: {
						type: 'object',
						properties: {},
						required: []
					}
				};

				// Converter parâmetros se existirem
				if (funcDecl.parameters && funcDecl.parameters.properties) {
					// Converter propriedades do schema Gemini para OpenAI
					const convertedProperties: any = {};

					for (const [propName, propSchema] of Object.entries(
						funcDecl.parameters.properties
					)) {
						const prop = propSchema as any;
						convertedProperties[propName] = {
							type:
								prop.type === 'Type.STRING' || prop.type === 1
									? 'string'
									: prop.type === 'Type.NUMBER' || prop.type === 2
									? 'number'
									: prop.type === 'Type.OBJECT' || prop.type === 5
									? 'object'
									: prop.type === 'Type.BOOLEAN' || prop.type === 4
									? 'boolean'
									: prop.type === 'Type.ARRAY' || prop.type === 6
									? 'array'
									: typeof prop.type === 'string'
									? prop.type.toLowerCase()
									: 'string',
							description: prop.description
						};

						// Adicionar propriedades adicionais se existirem
						if (prop.properties) {
							convertedProperties[propName].properties = prop.properties;
						}
					}

					convertedFunction.parameters.properties = convertedProperties;

					// Adicionar required fields se existirem
					if (funcDecl.parameters.required) {
						convertedFunction.parameters.required = funcDecl.parameters.required;
					}
				}

				return {
					type: 'function',
					function: convertedFunction
				};
			}
			return tool;
		});
		openRouterPayload.tools = openRouterTools;

		// console.log('🔧 Tools convertidas para OpenRouter:', JSON.stringify(openRouterTools, null, 2));
	}

	console.log(
		'📤 OpenRouter payload:',
		JSON.stringify(openRouterPayload, null, 2).substring(0, 599900)
	);

	try {
		const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${apiKey}`,
				'Content-Type': 'application/json',
				'HTTP-Referer': 'https://mitralab.io',
				'X-Title': 'Mitra AI Assistant'
			},
			body: JSON.stringify(openRouterPayload)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('❌ OpenRouter API error:', response.status, errorText);
			throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
		}

		const reader = response.body?.getReader();
		if (!reader) {
			throw new Error('❌ Não foi possível obter reader do response');
		}

		const decoder = new TextDecoder();
		let buffer = '';
		const functionCalls: any[] = [];
		let hasContent = false;

		// Acumuladores para reasoning e tool calls fragmentados
		let accumulatedReasoning = '';
		let accumulatedContent = ''; // Para validação de FinalSummary
		const toolCallsAccumulator: {
			[key: string]: { name?: string; arguments?: string };
		} = {};

		while (true) {
			const { done, value } = await reader.read();

			if (done) {
				console.log('✅ OpenRouter streaming concluído');
				break;
			}

			buffer += decoder.decode(value, { stream: true });
			const lines = buffer.split('\n');
			buffer = lines.pop() || '';

			for (const line of lines) {
				if (line.startsWith('data: ')) {
					const data = line.slice(6).trim();

					if (data === '[DONE]') {
						console.log('🏁 OpenRouter stream finalizado');
						continue;
					}

					try {
						const parsed = JSON.parse(data);

						// Converter formato OpenRouter para formato Gemini
						if (parsed.choices && parsed.choices[0]) {
							const choice = parsed.choices[0];
							const delta = choice.delta;

							// Processar reasoning tokens (thinking) do OpenRouter
							if (delta?.reasoning) {
								// Acumular reasoning fragmentado
								accumulatedReasoning += delta.reasoning;

								// Só enviar quando tiver conteúdo significativo (mais de 10 chars ou fim da linha)
								if (
									accumulatedReasoning.length > 10 ||
									delta.reasoning.includes('\n') ||
									choice.finish_reason
								) {
									console.log('🧠 OpenRouter reasoning acumulado:', accumulatedReasoning);
									const thinkingFormat = {
										candidates: [
											{
												content: {
													parts: [{ text: accumulatedReasoning, thought: true }],
													role: 'model'
												},
												finishReason: choice.finish_reason || null,
												index: 0
											}
										],
										usageMetadata: parsed.usage || {},
										modelVersion: parsed.model || model
									};
									res.write(`data: ${JSON.stringify(thinkingFormat)}\n\n`);
									accumulatedReasoning = ''; // Reset após enviar
								}
							}

							// Processar conteúdo normal
							if (delta?.content) {
								hasContent = true;

								// Acumular conteúdo para validação de FinalSummary
								accumulatedContent += delta.content;

								const geminiFormat = {
									candidates: [
										{
											content: {
												parts: [{ text: delta.content }],
												role: 'model'
											},
											finishReason: choice.finish_reason || null,
											index: 0
										}
									],
									usageMetadata: parsed.usage || {},
									modelVersion: parsed.model || model
								};
								res.write(`data: ${JSON.stringify(geminiFormat)}\n\n`);
							}

							// Tratar tool calls se houver
							if (delta?.tool_calls) {
								console.log('🔧 OpenRouter tool calls detectados:', delta.tool_calls);

								// Acumular tool calls fragmentados
								for (const toolCall of delta.tool_calls) {
									const toolId = toolCall.id || toolCall.index || '0';

									// Inicializar acumulador se não existir
									if (!toolCallsAccumulator[toolId]) {
										toolCallsAccumulator[toolId] = { name: '', arguments: '' };
									}

									// Acumular nome da função
									if (toolCall.function?.name) {
										toolCallsAccumulator[toolId].name = toolCall.function.name;
									}

									// Acumular argumentos (podem vir fragmentados)
									if (toolCall.function?.arguments) {
										toolCallsAccumulator[toolId].arguments += toolCall.function.arguments;
									}
								}

								// Verificar se algum tool call está completo (tem argumentos válidos JSON)
								for (const [, accumulated] of Object.entries(toolCallsAccumulator)) {
									if (accumulated.name && accumulated.arguments) {
										try {
											// Tentar parsear os argumentos acumulados
											const parsedArgs = JSON.parse(accumulated.arguments);

											// Se conseguiu parsear, é um tool call completo
											const geminiToolCall = {
												functionCall: {
													name: accumulated.name,
													args: parsedArgs
												}
											};

											const geminiFormat = {
												candidates: [
													{
														content: {
															parts: [geminiToolCall],
															role: 'model'
														},
														finishReason: choice.finish_reason || null,
														index: 0
													}
												],
												usageMetadata: parsed.usage || {},
												modelVersion: parsed.model || model
											};

											res.write(`data: ${JSON.stringify(geminiFormat)}\n\n`);

											// Adicionar à lista de function calls
											functionCalls.push({
												name: accumulated.name,
												args: parsedArgs
											});

											// Nota: Não limpar acumulador aqui pois pode haver mais fragmentos
										} catch (parseError) {
											// Argumentos ainda não estão completos, continuar acumulando
											console.log('🔧 Tool call ainda incompleto, continuando acumulação...');
										}
									}
								}
							}
						}
					} catch (parseError) {
						console.warn('⚠️ Erro ao parsear chunk OpenRouter:', parseError);
					}
				}
			}
		}

		// Processar tool calls incompletos quando stream termina (especialmente para Claude)
		if (Object.keys(toolCallsAccumulator).length > 0) {
			console.log('🔧 Processando tool calls incompletos ao final do stream...');

			for (const [, accumulated] of Object.entries(toolCallsAccumulator)) {
				// Se não tem nome mas tem argumentos, inferir que é run_sql (função mais comum)
				const functionName = accumulated.name || 'run_sql';

				if (accumulated.arguments) {
					try {
						// Tentar parsear argumentos incompletos
						let parsedArgs = {};

						// Se não conseguir parsear, tentar completar o JSON
						try {
							parsedArgs = JSON.parse(accumulated.arguments);
						} catch (parseError) {
							// Tentar completar JSON incompleto
							let fixedJson = accumulated.arguments;

							// Adicionar aspas fechando se necessário
							if (fixedJson.endsWith('"')) {
								// JSON já tem aspas finais
							} else if (fixedJson.includes('"') && !fixedJson.endsWith('"')) {
								fixedJson += '"';
							}

							// Adicionar chaves fechando se necessário
							const openBraces = (fixedJson.match(/\{/g) || []).length;
							const closeBraces = (fixedJson.match(/\}/g) || []).length;
							for (let i = 0; i < openBraces - closeBraces; i++) {
								fixedJson += '}';
							}

							console.log('🔧 Tentando corrigir JSON incompleto:', {
								original: accumulated.arguments,
								fixed: fixedJson
							});

							try {
								parsedArgs = JSON.parse(fixedJson);
								console.log('✅ JSON corrigido com sucesso!');
							} catch (fixError) {
								console.warn('⚠️ Não foi possível corrigir JSON, usando argumentos vazios');
								parsedArgs = {};
							}
						}

						// Adicionar à lista de function calls
						functionCalls.push({
							name: functionName,
							args: parsedArgs
						});

						console.log('🔧 Tool call incompleto processado:', {
							name: functionName,
							args: parsedArgs
						});
					} catch (error) {
						console.warn('⚠️ Erro ao processar tool call incompleto:', error);
					}
				}
			}
		}

		// Processar function calls se houver e não houve conteúdo de texto
		if (functionCalls.length > 0 && !hasContent && payload) {
			console.log(
				'🔧 Processando function calls do OpenRouter:',
				functionCalls.length
			);
			await processOpenRouterFunctionCalls(
				functionCalls,
				apiKey,
				model,
				messages,
				systemInstruction,
				res,
				tools,
				payload
			);
			return; // Não enviar chunk final se processou function calls
		}

		// Validar FinalSummary para OpenRouter (similar ao Gemini)
		const shouldCheckFinalSummaryOR = (payload as any)?.requireFinalSummary ?? true;
		if (shouldCheckFinalSummaryOR && hasContent) {
			const accumulatedTextString = accumulatedContent;
			const isPrematureStopOR =
				!accumulatedTextString.includes('</FinalSummary>') &&
				functionCalls.length === 0;

			if (isPrematureStopOR) {
				console.error('🚨 PREMATURE STOP DETECTED - OpenRouter parou prematuramente!');
				console.error('🚨 PREMATURE STOP - Critério: Ausência de <FinalSummary>', {
					model,
					hasContent,
					accumulatedLength: accumulatedTextString.length
				});

				// Emitir erro de premature stop
				const errorChunk = {
					type: 'error',
					error: {
						code: 'PREMATURE_STOP',
						message: 'OpenRouter stream stopped prematurely - missing FinalSummary',
						details: { model, hasContent }
					}
				};
				res.write(`data: ${JSON.stringify(errorChunk)}\n\n`);
				return;
			}
		}

		// Enviar chunk final apenas se não processou function calls
		const finalChunk = {
			candidates: [
				{
					content: {
						parts: [{ text: '' }],
						role: 'model'
					},
					finishReason: 'STOP',
					index: 0
				}
			],
			usageMetadata: {},
			modelVersion: model
		};
		res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
	} catch (error) {
		console.error('❌ Erro no streaming OpenRouter:', error);
		throw error;
	}
}

export default defineEventHandler(async (event) => {
	// 🚨 DEBUG: Variáveis para forçar erros - REMOVER EM PRODUÇÃO
	// 🚨 FLAGS DE TESTE - Mude para true para testar diferentes tipos de erro
	const FORCE_STREAM_ERROR = false; // ← Premature stop (timeout de chunks)
	const FORCE_429_ERROR = false; // ← Rate Limit (limite de requisições)
	const FORCE_API_KEY_ERROR = false; // ← API Key inválida (PERMISSION_DENIED)
	const FORCE_MODEL_NOT_FOUND = false; // ← Modelo não encontrado (NOT_FOUND)
	const FORCE_TIMEOUT_ERROR = false; // ← Timeout de requisição (DEADLINE_EXCEEDED)
	const FORCE_SERVICE_UNAVAILABLE = false; // ← Serviço indisponível (UNAVAILABLE)
	const FORCE_INVALID_ARGUMENT = false; // ← Argumentos inválidos (INVALID_ARGUMENT)
	const FORCE_QUOTA_EXCEEDED = false; // ← Cota excedida (RESOURCE_EXHAUSTED)
	const FORCE_NETWORK_ERROR = false; // ← Erro de rede genérico

	console.log(
		'🚀 SERVER - Endpoint /api/gemini-chat chamado, FORCE_429_ERROR =',
		FORCE_429_ERROR
	);

	// Retornar para requisições OPTIONS
	if (event.node.req.method === 'OPTIONS') {
		setResponseHeaders(event, {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization'
		});
		return { success: true };
	}

	// === SSE shared vars (hoisted to be visible across try/catch/finally) ===
	const req = event.node.req;
	const res = event.node.res;
	let heartbeat: NodeJS.Timeout | null = null;
	let closed = false;
	const cleanup = () => {
		if (closed) return;
		closed = true;
		if (heartbeat) clearInterval(heartbeat);
		try {
			if (!res.writableEnded) res.end();
		} catch {}
	};
	// === END shared vars ===

	try {
		const payload: GeminiChatPayload = await readBody(event);

		// Log dos novos campos para rastreamento
		console.log(
			`ℹ️  Requisição recebida de [Usuário: ${payload.username || 'N/A'}, Origem: ${
				payload.requestOrigin || 'N/A'
			}]`
		);

		if (payload.mitraLogData?.previousMaskedApiKey) {
			console.log(
				`🔄 Tentativa de Retry #${payload.mitraLogData.retryAttempt}. Chave anterior que falhou: ${payload.mitraLogData.previousMaskedApiKey}`
			);
		}

		if (!payload.authorizationToken) {
			throw createError({
				statusCode: 400,
				statusMessage: 'Token de autorização é obrigatório'
			});
		}

		// 🚨 TESTE DE ERROS FORÇADOS - Verificar flags antes de processar
		if (FORCE_API_KEY_ERROR) {
			console.error(
				'🚨 FORCE_API_KEY_ERROR ativado - Simulando erro de API key inválida'
			);
			const apiKeyError = new Error(
				'PERMISSION_DENIED: API key inválida ou sem permissões'
			);
			(apiKeyError as any).isGeminiError = true;
			(apiKeyError as any).isApiKeyInvalid = true;
			(apiKeyError as any).errorType = 'PERMISSION_DENIED';
			throw apiKeyError;
		}

		if (FORCE_MODEL_NOT_FOUND) {
			console.error(
				'🚨 FORCE_MODEL_NOT_FOUND ativado - Simulando modelo não encontrado'
			);
			const modelError = new Error(
				'NOT_FOUND: Modelo "gemini-1.5-pro-teste" não existe. Você quis dizer "gemini-1.5-pro"?'
			);
			(modelError as any).isGeminiError = true;
			(modelError as any).errorType = 'NOT_FOUND';
			throw modelError;
		}

		if (FORCE_TIMEOUT_ERROR) {
			console.error(
				'🚨 FORCE_TIMEOUT_ERROR ativado - Simulando timeout de requisição'
			);
			const timeoutError = new Error(
				'DEADLINE_EXCEEDED: Tempo limite da requisição excedido'
			);
			(timeoutError as any).isGeminiError = true;
			(timeoutError as any).errorType = 'DEADLINE_EXCEEDED';
			throw timeoutError;
		}

		if (FORCE_SERVICE_UNAVAILABLE) {
			console.error(
				'🚨 FORCE_SERVICE_UNAVAILABLE ativado - Simulando serviço indisponível'
			);
			const unavailableError = new Error(
				'UNAVAILABLE: Serviço temporariamente indisponível'
			);
			(unavailableError as any).isGeminiError = true;
			(unavailableError as any).errorType = 'UNAVAILABLE';
			throw unavailableError;
		}

		if (FORCE_INVALID_ARGUMENT) {
			console.error(
				'🚨 FORCE_INVALID_ARGUMENT ativado - Simulando argumentos inválidos'
			);
			const invalidArgError = new Error(
				'INVALID_ARGUMENT: Parâmetros inválidos para o modelo'
			);
			(invalidArgError as any).isGeminiError = true;
			(invalidArgError as any).errorType = 'INVALID_ARGUMENT';
			throw invalidArgError;
		}

		if (FORCE_QUOTA_EXCEEDED) {
			console.error('🚨 FORCE_QUOTA_EXCEEDED ativado - Simulando cota excedida');
			const quotaError = new Error(
				'RESOURCE_EXHAUSTED: Cota de requisições excedida'
			);
			(quotaError as any).isGeminiError = true;
			(quotaError as any).errorType = 'RESOURCE_EXHAUSTED';
			throw quotaError;
		}

		if (FORCE_NETWORK_ERROR) {
			console.error('🚨 FORCE_NETWORK_ERROR ativado - Simulando erro de rede');
			const networkError = new Error(
				'NETWORK_ERROR: Falha na conexão com o servidor'
			);
			(networkError as any).isGeminiError = true;
			(networkError as any).errorType = 'NETWORK_ERROR';
			throw networkError;
		}

		const baseUrl = payload.nuxtBaseUrl || 'https://api0.mitraecp.com:1005';
		const payloadUserToken = payload.nuxtToken ?? payload.authorizationToken;
		const isToUseUserIaCredit = payload.isToUseUserIaCredit ?? true;
		const isToUseOwnKey = payload.isToUseOwnKey ?? false;
		// Server side do customInstructions
		const serverSideCustomInstructions = payload.workspaceAiFlow
			? getWorkspaceAiFlowCustomInstructions({
					files: payload?.files || [],
					sessionId: payload.mitraLogData?.sessionId as string,
					isSankhya: payload.isSankhya ?? false
			  })
			: await getCustomInstructions({
					selectedAiMode: payload.configChain?.selectedAIMode ?? 'AI_ANALYST',
					selectedScreenId: payload.configChain?.selectedScreenId ?? 0,
					contextScreenId: payload.configChain?.contextScreenId ?? null,
					modalScreenId: payload.configChain?.modalScreenId ?? null,
					jdbcConfigId: payload.jdbcConfigId ?? 1,
					baseUrl: payload.baseUrl ?? 'https://api0.mitraecp.com:1005',
					payloadUserToken: payload.authorizationToken,
					isFinalUserChatSideBar: payload.configChain?.isFinalUserChatSideBar ?? false,
					selectedProjectAcessType: payload.selectedProjectAcessType,
					analyzeAllScreens: payload.analyzeAllScreens ?? false,
					isPlayground: payload.configChain?.isPlayground ?? false,
					isSankhya: payload.isSankhya ?? false,
					sessionId: payload.mitraLogData?.sessionId as string,
					projectName: payload.projectName
			  });

		console.log('✅ Passou do customInstructions');
		// console.log('customInstructions: ', serverSideCustomInstructions);

		// force build

		// Verificar se deve usar OpenRouter (padrão: true)
		const useOpenRouter = payload.useOpenRouter ?? false;
		console.log('🔄 Usar OpenRouter:', useOpenRouter);

		let apiKey: string;
		let ai: GoogleGenAI | null = null;

		if (useOpenRouter) {
			// Obter API key do OpenRouter
			apiKey = getOpenRouterApiKey(
				baseUrl,
				payloadUserToken,
				isToUseUserIaCredit,
				isToUseOwnKey,
				payload.selectedWorkspaceId,
				payload.configChain?.freeIaCreditsUsage ?? 0,
				payload.isSankhya ?? false
			);
			console.log('🔑 Usando API key do OpenRouter (mascarada):', maskKey(apiKey));
		} else {
			// Obter API key do Gemini (hardcoded)
			apiKey = await getGeminiApiKey(
				baseUrl,
				payloadUserToken,
				isToUseUserIaCredit,
				isToUseOwnKey,
				payload.selectedWorkspaceId,
				payload.configChain?.freeIaCreditsUsage ?? 0,
				payload.isSankhya ?? false,
				payload.mitraLogData?.retryAttempt,
				payload.mitraLogData?.previousMaskedApiKey ?? undefined
			);
			console.log('🔑 Usando API key do Gemini (mascarada):', maskKey(apiKey));

			// Inicializar Google GenAI apenas se não for OpenRouter
			ai = new GoogleGenAI({
				apiKey
			});
		}

		// === SSE INIT (early) ===
		setResponseHeaders(event, {
			'Content-Type': 'text/event-stream; charset=utf-8',
			'Cache-Control': 'no-cache, no-transform',
			'Access-Control-Allow-Origin': '*'
		});
		res.writeHead(200);
		res.flushHeaders?.();

		// Primeiros bytes para destravar proxies/CDN
		res.write('event: ready\ndata: {}\n\n');
		res.write(':\n\n');

		// Enviar a chave de API mascarada como um dos primeiros eventos
		try {
			const systemInfoPayload = {
				type: 'system_info',
				data: {
					maskedApiKey: maskKey(apiKey)
				},
				timestamp: Date.now()
			};
			res.write(`data: ${JSON.stringify(systemInfoPayload)}\n\n`);
		} catch (writeErr) {
			console.error('❌ Falha ao emitir SSE system_info:', writeErr);
		}

		// Heartbeat para manter a conexão viva
		const HEARTBEAT_MS = 15000;
		heartbeat = setInterval(() => {
			try {
				res.write(':\n\n');
			} catch {
				/* socket pode ter fechado */
			}
		}, HEARTBEAT_MS);

		let streamingStarted = true; // já estamos streamando
		req.on('close', cleanup);
		req.on('aborted', cleanup);
		res.on?.('close', cleanup);
		// === FIM SSE INIT ===

		// Função para limpar conteúdos (baseada no useGeminiAI.ts)
		const cleanContents = (contents: any[]) => {
			return contents
				.map((content) => {
					if (content.parts) {
						const cleanedParts = content.parts
							.filter((part: any) => {
								// Manter partes com texto válido OU com inlineData (imagens)
								return (part.text && part.text.trim() !== '') || part.inlineData;
							})
							.map((part: any) => {
								// Se tem inlineData (imagem), manter como está
								if (part.inlineData) {
									return part;
								}
								// Se tem texto, limpar o texto
								if (part.text) {
									return {
										text: part.text.replace(/\n\s*\n/g, '\n').trim()
									};
								}
								return part;
							});

						return {
							...content,
							parts: cleanedParts
						};
					}
					return content;
				})
				.filter((content) => content.parts && content.parts.length > 0);
		};

		// Preparar conteúdos para o Gemini
		const contents = [...payload.userPrompt];

		// Adicionar custom instructions
		contents.push({
			role: 'user',
			parts: [{ text: serverSideCustomInstructions }]
		});

		// Integrar arquivos no último prompt do usuário (se houver)
		if (payload.files && payload.files.length > 0) {
			// Encontrar o último prompt do usuário
			const lastUserContentIndex = contents
				.map((c, i) => ({ content: c, index: i }))
				.filter(({ content }) => content.role === 'user')
				.pop()?.index;

			if (lastUserContentIndex !== undefined) {
				const lastUserContent = contents[lastUserContentIndex];

				// Processar cada arquivo (já vem em base64 do cliente)
				for (const file of payload.files) {
					if (file && file.type && file.data) {
						try {
							// Validar formato base64
							const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
							const isValidBase64 = base64Pattern.test(file.data) && file.data.length > 0;

							console.log(`📎 GEMINI FILE DEBUG - Processando arquivo: ${file.name}`);
							console.log(`📋 GEMINI FILE DEBUG - Tipo: ${file.type}`);
							console.log(`📏 GEMINI FILE DEBUG - Tamanho base64: ${file.data.length} chars`);
							console.log(`✅ GEMINI FILE DEBUG - Base64 válido: ${isValidBase64}`);

							if (!isValidBase64) {
								console.error(`❌ GEMINI FILE DEBUG - Base64 inválido para ${file.name}`);
								continue;
							}

							// Verificar tipos de arquivo suportados pelo Gemini
							const supportedTypes = [
								// Imagens
								'image/png',
								'image/jpeg',
								'image/jpg',
								'image/webp',
								'image/gif',
								// Documentos
								'application/pdf',
								'text/plain',
								'text/csv',
								'text/html',
								'text/css',
								'text/javascript',
								// Documentos Office
								'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
								'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
								'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
								// Outros
								'application/rtf',
								'text/rtf'
							];

							if (supportedTypes.includes(file.type)) {
								(lastUserContent.parts as any).push({
									inlineData: {
										data: file.data,
										mimeType: file.type
									}
								});

								console.log(
									`✅ GEMINI FILE DEBUG - Arquivo adicionado com sucesso: ${file.name} (${file.type})`
								);
							} else {
								console.warn(
									`⚠️ GEMINI FILE DEBUG - Tipo de arquivo não suportado: ${file.type} para ${file.name}`
								);
							}
						} catch (error) {
							console.error('❌ GEMINI FILE DEBUG - Erro ao processar arquivo:', error);
						}
					} else {
						console.log(
							`📎 GEMINI FILE DEBUG - Arquivo inválido ou sem dados: ${
								file?.name || 'unknown'
							}`
						);
					}
				}
			}
		}

		const cleanedContents = cleanContents(contents);

		// Log detalhado dos contents para debug de visão
		// CHAIN gigante que espama o terminal quando é muito grande.
		// console.log('🔍 GEMINI VISION DEBUG - Contents ANTES do cleanCon');
		// console.log(JSON.stringify(contents, null, 2).substring(0, 59990));
		console.log('🔍 GEMINI VISION DEBUG - Contents DEPOIS do cleanCo');
		console.log(JSON.stringify(cleanedContents, null, 2).substring(0, 199950));

		// Verificar se há arquivos (imagens/documentos) nos contents
		const hasFiles = cleanedContents.some(
			(content) => content.parts && content.parts.some((part: any) => part.inlineData)
		);
		console.log(`� GEMINI FILE DEBUG - Contém arquivos: ${hasFiles}`);

		if (hasFiles) {
			console.log(`🤖 GEMINI FILE DEBUG - Modelo usado: ${payload.model}`);
			console.log('📋 GEMINI FILE DEBUG - Verificando se modelo suporta arquivos...');

			// Verificar se o modelo suporta arquivos (visão + documentos)
			const fileSupportedModels = [
				'gemini-2.5-pro',
				'gemini-2.5-flash',
				'gemini-2.0-flash-exp',
				'gemini-1.5-pro',
				'gemini-1.5-flash'
			];

			const supportsFiles = fileSupportedModels.some((model) =>
				payload.model.includes(model)
			);
			console.log(`� GEMINI FILE DEBUG - Modelo suporta arquivos: ${supportsFiles}`);

			if (!supportsFiles) {
				console.warn(
					`⚠️ GEMINI FILE DEBUG - ATENÇÃO: Modelo ${payload.model} pode não suportar arquivos!`
				);
			}

			// Listar tipos de arquivos detectados
			const fileTypes = new Set<string>();
			cleanedContents.forEach((content) => {
				if (content.parts) {
					content.parts.forEach((part: any) => {
						if (part.inlineData && part.inlineData.mimeType) {
							fileTypes.add(part.inlineData.mimeType);
						}
					});
				}
			});
			console.log(
				`📋 GEMINI FILE DEBUG - Tipos de arquivo detectados: ${Array.from(
					fileTypes
				).join(', ')}`
			);
		}

		// Configurar tools baseado nas configurações (EXATO do useGeminiAI.ts)
		// Tool exclusivity rules:
		// 1. Code Execution + Google Search Grounding = Compatible ✅
		// 2. Code Execution + Function Calling (SQL) = Incompatible ❌
		// 3. Google Search Grounding + Function Calling (SQL) = Incompatible ❌
		const tools: any[] = [];

		// Add Google Search grounding if enabled
		if (payload.googleSearchGrounding) {
			tools.push({
				googleSearch: {}
			});
			console.log('🔍 Google Search grounding enabled');
		}

		// Add Code Execution if enabled (compatible with Google Search)
		if (payload.codeExecutionEnabled) {
			tools.push({
				codeExecution: {}
			});
			console.log('🐍 Code Execution enabled');
		}

		// Add SQL function calling only if neither Google Search nor Code Execution is enabled
		if (!payload.googleSearchGrounding && !payload.codeExecutionEnabled) {
			tools.push({
				functionDeclarations: [
					{
						name: 'run_sql',
						description:
							'Execute SQL queries on the database to retrieve or manipulate data. Use this when you need to get specific information from the database.',
						parameters: {
							type: Type.OBJECT,
							properties: {
								query: {
									type: Type.STRING,
									description: 'The SQL query to execute, e.g. SELECT * FROM users WHERE id = 1'
								},
								jdbcConfigId: {
									type: Type.NUMBER,
									description: 'The JDBC config ID to use for the query'
								}
							},
							required: ['query', 'jdbcConfigId']
						}
					},
					{
						name: 'run_actions',
						description:
							'Executes a specific action by name, optionally using key/value variables and user-provided instructions.',
						parameters: {
							type: Type.OBJECT,
							properties: {
								actionName: {
									type: Type.STRING,
									description: 'The exact name of the action to execute.'
								},
								variables: {
									type: Type.OBJECT,
									description:
										'Key-value pairs representing input variables for the action. Example: { ":VAR_USUARIOGUEST": "john.doe", ":VAR_DT_FIM": "2025-07-01" }'
								}
							},
							required: ['actionName']
						}
					}
				]
			});
			console.log('🗄️ SQL function calling enabled');
		} else if (payload.googleSearchGrounding || payload.codeExecutionEnabled) {
			console.log(
				'🚫 SQL function calling disabled (incompatible with Google Search or Code Execution)'
			);
		}

		// Buscar CHAIN do Mitra API se configChain estiver presente
		let systemInstruction = payload.systemInstruction;
		if (payload.configChain) {
			const { isFinalUserChatSideBar, guidelinesToolsActions } = payload.configChain;

			if (isFinalUserChatSideBar) {
				console.log(
					'⚠️ CHAIN BLOCKED - Ignorando CHAIN por isFinalUserChatSideBar=true'
				);
				const schema = payload.configChain?.currentUserToolSchema;
				const schemaString = schema ? JSON.stringify(schema, null, 2) : '';
				const baseInstruction = guidelinesToolsActions?.trim() || '';
				systemInstruction = `${baseInstruction}\n\nUser Tool Schema:\n${schemaString}`;
			} else {
				console.log('🔗 CHAIN DEBUG - Processando configChain no server-side');
				try {
					systemInstruction = await getChainFromMitraAPI(payload.configChain);
					console.log('✅ CHAIN DEBUG - CHAIN processada com sucesso no server-side');
				} catch (error) {
					console.error(
						'❌ CHAIN DEBUG - Erro ao processar CHAIN no server-side:',
						error
					);
				}
			}
		}

		// Se grounding está habilitado, adicionar instrução para forçar uso das informações
		if (payload.googleSearchGrounding) {
			systemInstruction += `\n\nIMPORTANTE SOBRE GROUNDING:
        1. Quando informações de busca na web estiverem disponíveis, você DEVE utilizá-las na sua resposta
        2. Sempre cite e referencie as informações encontradas nas buscas
        3. Para perguntas sobre notícias, eventos atuais, cotações, clima, etc., use OBRIGATORIAMENTE as informações das buscas
        4. Não responda apenas com seu conhecimento interno quando há informações atualizadas disponíveis
        5. Integre as informações das buscas de forma natural na resposta`;
		}

		// Desabilitar tools se for AI_BUILDER
		const isAIBuilderMode = payload?.configChain?.selectedAIMode === 'AI_BUILDER';

		if (isAIBuilderMode) {
			console.log('🚫 AI_BUILDER detectado - Tools desabilitadas');
		}

		const toolConfig = {
			systemInstruction,
			temperature: clamp(0.01, 0, 1),
			tools: isAIBuilderMode ? undefined : tools,
			thinkingConfig: {
				includeThoughts: true
			}
		};

		// Validar modelo e temperatura antes de chamada ao Gemini
		validateModelAndParams(payload.model, toolConfig?.temperature);

		// Fazer chamada inicial com streaming, com retry/backoff
		let initialResponse;
		try {
			console.log(
				`🚀 Iniciando chamada para modelo: ${payload.model} ${
					useOpenRouter ? '(OpenRouter)' : '(Gemini)'
				}`
			);

			if (useOpenRouter) {
				// Usar o modelo selecionado diretamente do payload
				let openRouterModel = payload.model || 'google/gemini-2.5-pro';

				// Mapear modelos legados para compatibilidade
				const modelMappings: { [key: string]: string } = {
					// Gemini models
					'gemini-2.5-pro': 'google/gemini-2.5-pro',
					'gemini-2.5-flash': 'google/gemini-2.5-flash',
					'gemini-1.5-pro': 'google/gemini-pro-1.5',
					'gemini-1.5-flash': 'google/gemini-flash-1.5',

					// Claude models (NOMES CORRETOS 2025)
					'claude-sonnet-4': 'anthropic/claude-sonnet-4',
					'claude-4-sonnet': 'anthropic/claude-sonnet-4', // Fallback para nome incorreto
					'claude-3-5-sonnet-20241022': 'anthropic/claude-3-5-sonnet-20241022',
					'claude-3-opus': 'anthropic/claude-3-opus',

					// OpenAI models (2025)
					'gpt-5': 'openai/gpt-5',
					'gpt-4o': 'openai/gpt-4o',
					'gpt-4-turbo': 'openai/gpt-4-turbo',

					// xAI models (2025)
					'grok-4': 'x-ai/grok-4',
					'grok-3-beta': 'x-ai/grok-3-beta',

					// DeepSeek models (2025)
					'deepseek-v3.1': 'deepseek/deepseek-v3.1',
					'deepseek-r1': 'deepseek/deepseek-r1',
					'deepseek-r1-zero': 'deepseek/deepseek-r1-zero',
					'deepseek-chat': 'deepseek/deepseek-chat',

					// Qwen models (2025)
					'qwen3-coder': 'qwen/qwen3-coder',
					'qwen-2.5-coder-32b-instruct': 'qwen/qwen-2.5-coder-32b-instruct'
				};

				// Se o modelo está no mapeamento, usar o valor mapeado
				if (modelMappings[openRouterModel]) {
					openRouterModel = modelMappings[openRouterModel];
				}
				// Se o modelo já está no formato OpenRouter (provider/model), usar diretamente
				else if (openRouterModel.includes('/')) {
					// Modelo já está no formato correto do OpenRouter
					console.log('🤖 Usando modelo OpenRouter diretamente:', openRouterModel);
				}
				// Fallback para modelos não reconhecidos
				else {
					console.warn(
						'⚠️ Modelo não reconhecido, usando Gemini 2.5 Pro como fallback:',
						openRouterModel
					);
					openRouterModel = 'google/gemini-2.5-pro';
				}

				console.log('🤖 Modelo selecionado:', payload.model);
				console.log('🤖 Modelo OpenRouter final:', openRouterModel);

				await streamOpenRouterChat(
					apiKey,
					openRouterModel,
					cleanedContents,
					systemInstruction,
					res,
					tools.length > 0 && payload?.configChain?.selectedAIMode !== 'AI_BUILDER'
						? tools
						: undefined,
					payload
				);

				// Para OpenRouter, não precisamos processar stream adicional
				try {
					if (!res.writableEnded) res.end();
				} catch {}
				return;
			} else {
				// Usar Gemini
				validateModelAndParams(payload.model, (toolConfig as any)?.temperature);

				if (!ai) {
					throw new Error('Google GenAI não foi inicializado');
				}

				initialResponse = await withRetry(
					() =>
						ai.models.generateContentStream({
							model: payload.model,
							contents: cleanedContents,
							config: toolConfig
						}),
					{
						attempts: 3,
						baseDelayMs: 500,
						maxDelayMs: 8000,
						label: 'gemini.generateContentStream'
					}
				);
				console.log('✅ GEMINI VISION DEBUG - Chamada iniciada com sucesso');
			}
		} catch (error: any) {
			console.error('❌ Erro na chamada inicial:', error?.message || error);

			// SEMPRE envia erro via SSE chunk (streaming já foi iniciado)
			emitSSEError(event.node.res, error);
			try {
				if (!event.node.res.writableEnded) event.node.res.end();
			} catch {}
			return;
		}

		// Processar stream de resposta
		const functionCalls: any[] = [];
		const modelParts: any[] = [];
		let groundingMetadata: any = null; // Capturar grounding metadata (igual ao useGeminiAI.ts)

		// Throttling para processamento de chunks (igual ao useGeminiAI.ts)
		let pendingChunks: string[] = [];
		let throttleTimeout: NodeJS.Timeout | null = null;
		const THROTTLE_DELAY = 300; // 300ms de delay para agrupar chunks (otimizado original)

		// Variáveis para detectar parada prematura do Gemini (abordagem inteligente)
		let totalChunksReceived = 0;
		let totalTextLength = 0;
		let hasValidObjects = false;
		let lastChunkTime = Date.now();
		// let accumulatedText = '';
		const streamStartTime = Date.now();
		let lastFinishReason: string | undefined;

		// Função para enviar chunk via stream no formato original do Gemini
		const sendChunk = (chunk: StreamChunk) => {
			if (closed) return '';
			const data = `data: ${JSON.stringify(chunk)}\n\n`;
			try {
				res.write(data);
			} catch {
				cleanup();
			}
			return data;
		};

		// Função para encerrar stream com erro (emitir chunk SSE estruturado ou throw)
		const endStreamWithError = (error: any) => {
			try {
				emitSSEError(res, error);
			} catch {}
			cleanup();
		};

		// Função para extrair objetos válidos do texto (baseada no useAnswerStreamProcessor.ts)
		const extractValidObjectsFromText = (text: string): any[] => {
			const objects: any[] = [];

			console.log('🔍 EXTRACT DEBUG - Texto recebido:', text.substring(0, 399900));

			// Primeiro tenta extrair conteúdo das tags PlanningDevelopmentMitra
			const planningRegex =
				/<PlanningDevelopmentMitra>([\s\S]*?)(<\/PlanningDevelopmentMitra>|$)/g;
			let matches = [...text.matchAll(planningRegex)];

			console.log(
				'🔍 EXTRACT DEBUG - Matches PlanningDevelopmentMitra encontrados:',
				matches.length
			);

			// Se não encontrou PlanningDevelopmentMitra, tenta BuildingSystem para compatibilidade
			if (matches.length === 0) {
				const buildingSystemRegex = /<BuildingSystem>([\s\S]*?)(<\/BuildingSystem>|$)/g;
				matches = [...text.matchAll(buildingSystemRegex)];

				console.log(
					'🔍 EXTRACT DEBUG - Matches BuildingSystem encontrados:',
					matches.length
				);
			}

			if (matches.length === 0) {
				// Se não encontrou nenhuma das tags, procura diretamente por arrays
				console.log('🔍 EXTRACT DEBUG - Procurando arrays diretamente no texto');
				return extractArraysDirectly(text);
			}

			for (const match of matches) {
				const content = match[1].trim();
				console.log(
					'🔍 EXTRACT DEBUG - Conteúdo BuildingSystem:',
					content.substring(0, 299900)
				);

				// Procura por arrays de objetos usando TODAS as chaves do superAIPlanKeys
				const superAIPlanKeys = [
					'ADD_ACTIONS',
					'ADD_ATRIBUTES_DB',
					'ADD_COMPONENT_BUTTON',
					'ADD_COMPONENT_CONTAINER',
					'ADD_COMPONENT_CRUD_LIST',
					'ADD_COMPONENT_GRAPH',
					'ADD_COMPONENT_HTML',
					'ADD_COMPONENT_IMAGEM',
					'ADD_COMPONENT_IMAGE',
					'ADD_COMPONENT_PIECHART',
					'ADD_COMPONENT_BARCHART',
					'ADD_COMPONENT_INPUT',
					'ADD_COMPONENT_KANBAN',
					'ADD_COMPONENT_LABEL',
					'ADD_COMPONENT_LIST',
					'UPLOAD_PUBLIC_FILE',
					'UPLOAD_LOADABLE_FILE',
					'ADD_COMPONENT_REACT',
					'ADD_COMPONENT_GENERAL_LIST',
					'ADD_COMPONENT_GENERAL_KANBAN',
					'ADD_COMPONENT_SELECTOR',
					'ADD_COMPONENT_TABLE',
					'ADD_DBACTIONS',
					'ALT_DBACTIONS',
					'ADD_IMPORT',
					'USE_FILES_AS_PUBLIC',
					'USE_FILES_AS_LOADABLE',
					'ADD_DETAILS_MODAL',
					'ADD_FORMS',
					'ADD_TABELAS_DB',
					'ADD_CUBES_DB',
					'ADD_DASHBOARD',
					'ADD_TELAS',
					'ADD_VARIAVEIS',
					'ADD_WIDGET',
					'ALT_ACTIONS',
					'ALT_ATRIBUTES_DB',
					'ALT_COMPONENT_BUTTON',
					'ALT_COMPONENT_CONTAINER',
					'ALT_COMPONENT_CRUD_LIST',
					'ALT_COMPONENT_GRAPH',
					'ALT_COMPONENT_HTML',
					'ALT_COMPONENT_IMAGEM',
					'ALT_COMPONENT_IMAGE',
					'ALT_COMPONENT_PIECHART',
					'ALT_COMPONENT_BARCHART',
					'ALT_COMPONENT_INPUT',
					'ALT_COMPONENT_KANBAN',
					'ALT_COMPONENT_LABEL',
					'ALT_COMPONENT_LIST',
					'ALT_COMPONENT_REACT',
					'ALT_COMPONENT_GENERAL_LIST',
					'ALT_COMPONENT_GENERAL_KANBAN',
					'ALT_COMPONENT_SELECTOR',
					'ALT_COMPONENT_TABLE',
					'ALT_DETAILS_MODAL',
					'ALT_FORMS',
					'ALT_TABELAS_DB',
					'ALT_CUBES_DB',
					'ALT_DASHBOARD',
					'ALT_TELAS',
					'DELETE_ATRIBUTES_DB',
					'DELETE_COMPONENT',
					'DELETE_DBACTIONS',
					'DELETE_DETAILS_MODAL',
					'DELETE_FORMS',
					'DELETE_CUBES_DB',
					'DELETE_TABELAS_DB',
					'DELETE_TAB_DETAILS_MODAL',
					'DELETE_DASHBOARD',
					'DELETE_TELAS',
					'DELETE_VARIAVEIS',
					'RUN_DDL',
					'RUN_DML',
					'UPLOAD_PUBLIC_FILE',
					'UPLOAD_LOADABLE_FILE'
				];

				const arrayPatterns = superAIPlanKeys.map((key) => ({
					pattern: new RegExp(`"${key}":\\s*\\[([\\s\\S]*?)(\\]|$)`, 'g'),
					type: key
				}));

				for (const { pattern, type } of arrayPatterns) {
					const arrayMatches = [...content.matchAll(pattern)];
					console.log(`🔍 EXTRACT DEBUG - Matches para ${type}:`, arrayMatches.length);

					for (const arrayMatch of arrayMatches) {
						const arrayContent = arrayMatch[1];
						console.log(`� EXTRACT DEBUG - Conteúdo do array ${type}:`, arrayContent);

						// Extrai objetos individuais do array (completos e parciais)
						const extractedObjects = extractObjectsFromArrayContent(arrayContent, type);
						objects.push(...extractedObjects);
					}
				}
			}

			console.log('🔍 EXTRACT DEBUG - Total de objetos extraídos:', objects.length);

			// Atualizar flag de objetos válidos se encontrou algum
			if (objects.length > 0) {
				hasValidObjects = true;
			}

			return objects;
		};

		// Função auxiliar para extrair arrays diretamente do texto
		const extractArraysDirectly = (text: string): any[] => {
			const objects: any[] = [];
			const arrayPatterns = [
				{ pattern: /"ADD_TELAS":\s*\[([\s\S]*?)(\]|$)/g, type: 'ADD_TELAS' },
				{ pattern: /"ADD_DASHBOARD":\s*\[([\s\S]*?)(\]|$)/g, type: 'ADD_DASHBOARD' }
			];

			for (const { pattern, type } of arrayPatterns) {
				const matches = [...text.matchAll(pattern)];
				for (const match of matches) {
					const arrayContent = match[1];
					const extractedObjects = extractObjectsFromArrayContent(arrayContent, type);
					objects.push(...extractedObjects);
				}
			}

			return objects;
		};

		// Função auxiliar para extrair objetos de conteúdo de array
		const extractObjectsFromArrayContent = (
			arrayContent: string,
			arrayType: string
		): any[] => {
			const objects: any[] = [];

			// Procura por objetos completos primeiro
			const completeObjectMatches = arrayContent.match(/\{[^{}]*\}/g);
			if (completeObjectMatches) {
				for (const objStr of completeObjectMatches) {
					try {
						const parsed = JSON.parse(objStr);
						objects.push({
							...parsed,
							_arrayType: arrayType,
							_extractedAt: Date.now(),
							_isPartial: false
						});
						console.log(
							`✅ EXTRACT DEBUG - Objeto completo extraído de ${arrayType}:`,
							parsed
						);
					} catch (e) {
						console.log(`❌ EXTRACT DEBUG - Erro ao parsear objeto completo:`, objStr);
					}
				}
			}

			// Se não encontrou objetos completos, procura por objetos parciais
			if (objects.length === 0) {
				const partialObjectRegex = /\{\s*"[^"]+"\s*:\s*"[^"]*"/g;
				const partialMatches = [...arrayContent.matchAll(partialObjectRegex)];

				for (const match of partialMatches) {
					const partialObj = match[0];
					// Tenta completar o objeto parcial
					const attempts = [
						partialObj + '}',
						partialObj + '"}',
						partialObj + ', "height": 300}'
					];

					for (const attempt of attempts) {
						try {
							const parsed = JSON.parse(attempt);
							objects.push({
								...parsed,
								_arrayType: arrayType,
								_extractedAt: Date.now(),
								_isPartial: true
							});
							console.log(
								`✅ EXTRACT DEBUG - Objeto parcial extraído de ${arrayType}:`,
								parsed
							);
							break;
						} catch (e) {
							// Continua tentando
						}
					}
				}
			}

			return objects;
		};

		// Função para processar chunks throttled (baseada no useGeminiAI.ts)
		const processChunksThrottled = () => {
			if (pendingChunks.length === 0) return;

			// Junta todos os chunks pendentes em um único texto
			const combinedText = pendingChunks.join('');
			console.log(
				'🔄 THROTTLE DEBUG - Processando chunks combinados:',
				combinedText.substring(0, 299900)
			);
			pendingChunks = [];

			// Processa o texto combinado para extrair objetos válidos
			const newValidObjects = extractValidObjectsFromText(combinedText);
			console.log('🎯 THROTTLE DEBUG - Objetos extraídos:', newValidObjects.length);

			// Se encontrou novos objetos válidos, envia via stream
			if (newValidObjects.length > 0) {
				console.log(
					'✅ THROTTLE DEBUG - Enviando objetos via stream:',
					newValidObjects
				);
				newValidObjects.forEach((obj) => {
					sendChunk({
						type: 'validObject',
						object: obj,
						timestamp: Date.now()
					});
				});
			}
		};

		// Função para adicionar chunk ao processamento throttled
		const addChunkToProcess = (text: string) => {
			console.log(
				'🔄 THROTTLE DEBUG - Adicionando chunk:',
				text.substring(0, 199900)
			);
			pendingChunks.push(text);

			// Cancela o timeout anterior se existir
			if (throttleTimeout) {
				clearTimeout(throttleTimeout);
			}

			// Agenda o processamento após o delay
			throttleTimeout = setTimeout(processChunksThrottled, THROTTLE_DELAY);
		};

		// Processar resposta inicial - enviar response completo como o useGeminiAI.ts fazia
		try {
			for await (const response of initialResponse) {
				// Marcar que o streaming começou no primeiro chunk
				if (!streamingStarted) {
					streamingStarted = true;
					console.log('🚀 STREAMING STARTED - Primeiro chunk recebido');
				}

				// Rastrear chunks recebidos para detectar parada prematura
				totalChunksReceived++;
				lastChunkTime = Date.now();

				// Capture finish reason if the SDK provides it
				const fr = response?.candidates?.[0]?.finishReason;
				if (fr) lastFinishReason = String(fr);

				// Capturar grounding metadata se disponível (LOG TODOS OS CHUNKS)
				if (response.candidates?.[0]?.groundingMetadata) {
					const newMetadata = response.candidates[0].groundingMetadata;

					console.log('🔍 GROUNDING DEBUG - CHUNK RECEBIDO:', {
						hasGroundingSupports: !!newMetadata.groundingSupports,
						hasSearchEntryPoint: !!newMetadata.searchEntryPoint,
						hasWebSearchQueries: !!newMetadata.webSearchQueries,
						groundingSupportsCount: newMetadata.groundingSupports?.length || 0,
						webSearchQueriesCount: newMetadata.webSearchQueries?.length || 0,
						keys: Object.keys(newMetadata)
					});

					// SEMPRE substitui para ver todos os chunks
					groundingMetadata = newMetadata;
					console.log('🔍 GROUNDING DEBUG - Metadata atualizado (initial)');
				}

				// Enviar a resposta completa no formato original do Gemini (como era no useGeminiAI.ts)
				sendChunk(response);

				// Processar function calls e chunks de texto para execução no server-side
				if (response.candidates?.[0]?.content?.parts) {
					for (const part of response.candidates[0].content.parts) {
						if (part.functionCall) {
							functionCalls.push(part.functionCall);
							modelParts.push(part);
						} else if (part.text) {
							// Rastrear comprimento total do texto e acumular para análise
							totalTextLength += part.text.length;
							// accumulatedText += part.text;
							// Adicionar chunk de texto ao processamento throttled (igual ao useGeminiAI.ts)
							addChunkToProcess(part.text);
						}
					}
				}
			}
		} catch (streamError: any) {
			console.error('🚨 ERRO DURANTE STREAMING:', streamError);
			endStreamWithError(streamError);
			return; // Sai da função para evitar processamento adicional
		}

		// Processar function calls se existirem
		if (functionCalls.length > 0) {
			try {
				const functionCallMetadata = await processFunctionCalls(
					functionCalls,
					modelParts,
					ai,
					payload,
					cleanedContents,
					toolConfig,
					event,
					sendChunk,
					addChunkToProcess
				);

				// Se function calls retornaram grounding metadata mais completo, usar ele
				if (
					functionCallMetadata &&
					(!groundingMetadata || functionCallMetadata.groundingSupports)
				) {
					groundingMetadata = functionCallMetadata;
					console.log('🔍 GROUNDING DEBUG - Metadata de function calls usado como final');
				}
			} catch (functionCallError: any) {
				console.error('🚨 ERRO DURANTE FUNCTION CALLS:', functionCallError);
				endStreamWithError(functionCallError);
				return; // Sai da função para evitar processamento adicional
			}
		}

		// Metadata de grounding já foi enviada junto com as respostas originais

		// Processa qualquer chunk pendente antes de finalizar (igual ao useGeminiAI.ts)
		if (throttleTimeout) {
			clearTimeout(throttleTimeout);
			throttleTimeout = null;
		}
		if (pendingChunks.length > 0) {
			processChunksThrottled();
		}

		// Enviar grounding metadata final se disponível (igual ao useGeminiAI.ts)
		if (groundingMetadata) {
			console.log('🔍 GROUNDING DEBUG - METADATA FINAL SENDO ENVIADO:');
			console.log('🔍 GROUNDING DEBUG - Resumo:', {
				hasGroundingSupports: !!groundingMetadata.groundingSupports,
				hasSearchEntryPoint: !!groundingMetadata.searchEntryPoint,
				hasWebSearchQueries: !!groundingMetadata.webSearchQueries,
				groundingSupportsCount: groundingMetadata.groundingSupports?.length || 0,
				webSearchQueriesCount: groundingMetadata.webSearchQueries?.length || 0,
				allKeys: Object.keys(groundingMetadata)
			});
			console.log(
				'🔍 GROUNDING DEBUG - Metadata completo:',
				JSON.stringify(groundingMetadata, null, 2).substring(0, 59990)
			);

			// Se não tem groundingSupports, vamos investigar por que
			if (!groundingMetadata.groundingSupports) {
				console.log('⚠️ GROUNDING DEBUG - ATENÇÃO: Metadata sem groundingSupports!');
				console.log(
					'⚠️ GROUNDING DEBUG - Isso pode indicar que o Gemini não usou as informações do grounding na resposta'
				);
			}

			sendChunk({
				type: 'groundingMetadata',
				groundingMetadata,
				timestamp: Date.now()
			});
		} else {
			console.log('🔍 GROUNDING DEBUG - Nenhum metadata para enviar');
		}

		// DETECTAR PARADA PREMATURA DO GEMINI (Abordagem Inteligente)
		const streamDuration = Date.now() - streamStartTime;
		const timeSinceLastChunk = Date.now() - lastChunkTime;

		console.log('🔍 STREAM ANALYSIS - Estatísticas finais:', {
			totalChunksReceived,
			totalTextLength,
			hasValidObjects,
			streamDuration: `${streamDuration}ms`,
			timeSinceLastChunk: `${timeSinceLastChunk}ms`,
			hasGroundingMetadata: !!groundingMetadata,
			hasFunctionCalls: functionCalls.length > 0,
			finishReason: lastFinishReason
		});

		// Verificar se a resposta tem conclusão adequada (apenas para debug; não é critério de erro)
		const sawAnyChunks = totalChunksReceived > 0;
		const hasAnyModelText = totalTextLength > 0;
		const anyOutput =
			sawAnyChunks &&
			(hasAnyModelText || hasValidObjects || functionCalls.length > 0);
		const gracefulStop = lastFinishReason
			? String(lastFinishReason).toUpperCase()
			: undefined;

		// Critério simples e eficaz para detectar parada prematura:
		// Se não tem <FinalSummary>, é porque perdeu conexão (premature stop)
		// Mas só verifica se FinalSummary foi requerido

		// Verifica se o texto acumulado contém a tag de conclusão <FinalSummary> mas da bypass caso esteja em fluxo de function calls
		// const accumulatedTextString = JSON.stringify(accumulatedText, null, 2);
		// const shouldCheckFinalSummary = (payload as any).requireFinalSummary ?? true;
		const isPrematureStop = false;

		// 🚨 DEBUG: Simular erro 429 durante o streaming se a flag estiver ativa
		if (FORCE_429_ERROR) {
			console.error(
				'🚨 FORCE_429_ERROR ativado - Simulando erro 429 durante o streaming'
			);
			const error429 = new Error('[GEMINI_ERROR] Limite de requisições excedido.');
			error429.name = 'RATE_LIMIT_EXCEEDED';
			(error429 as any).isGeminiError = true;
			(error429 as any).isApiKeyInvalid = true;
			(error429 as any).errorType = 'RATE_LIMIT_EXCEEDED';
			(error429 as any).data = { text: 'Code: 429\n' };
			endStreamWithError(error429);
			return;
		}

		if (isPrematureStop || FORCE_STREAM_ERROR) {
			console.error('🚨 PREMATURE STOP DETECTED - Gemini parou prematuramente!');
			console.error('🚨 PREMATURE STOP - Critério: Ausência de <FinalSummary>', {
				gracefulStop,
				finishReason: lastFinishReason,
				forceError: FORCE_STREAM_ERROR,
				streamStats: {
					totalChunksReceived,
					totalTextLength,
					hasValidObjects,
					hasAnyModelText,
					anyOutput,
					streamDuration,
					timeSinceLastChunk
				}
			});

			// Encerrar stream com erro apenas quando realmente não houve saída útil
			const prematureStopError = new Error(
				'Gemini AI error: Não obtivemos resposta do Gemini'
			);
			(prematureStopError as any).isGeminiError = true;
			(prematureStopError as any).errorType = 'PREMATURE_STOP';
			(prematureStopError as any).data = { text: 'Não obtivemos resposta do Gemini' };
			endStreamWithError(prematureStopError);
			return;
		}

		console.log('✅ STREAM COMPLETED SUCCESSFULLY - Stream finalizado normalmente');

		if (payload.mitraLogData.isRetryMessage) {
			console.log('ℹ️ Mensagem de retry detectada - Ignorando log para Mitra');
		}

		if (
			payload.mitraLogData &&
			!payload.workspaceAiFlow &&
			!payload.mitraLogData.isRetryMessage
		) {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			const {
				messageId,
				projectId,
				sessionId,
				inputValue,
				isRetryMessage,
				retryAttempt
			} = payload.mitraLogData;
			const msgError = false;
			const messageDoErro = msgError ? 'Erro ao processar mensagem' : '';

			await logSuperAIMessages({
				payloadUserToken: payload.nuxtToken as string,

				messageData: {
					projectId,
					messageId,
					message: inputValue,
					threadId: sessionId,
					messageType: msgError
						? 'SUPERIA-ERROR'
						: isRetryMessage
						? `SUPERIA-RETRY-${retryAttempt}`
						: 'SUPERIA',
					errorMessage: msgError ? messageDoErro : null,
					keyUsed: (ai as any).apiKey
				},

				operationData: {
					operation: 'Finish SUPER AI with success',
					modelName: payload.model,
					messageId,
					dateInit: new Date().toISOString(),
					dateEnd: new Date().toISOString(),
					agent: 'Gemini API', // verificar o dinamico pelo open router
					conclusion: 'finished request',
					tokenIn: 0, // encontrar valores disso com waguim
					tokenOut: 0, // encontrar valores disso com waguim,
					reasoningTokens: 0 // encontrar valores disso com waguim
				}
			});
		}

		// Finalizar o stream corretamente
		cleanup();
	} catch (error: any) {
		console.error('Erro no endpoint Gemini:', error?.message || error);

		// Como o streaming sempre é iniciado, envia erro via SSE chunk
		try {
			emitSSEError(res, error);
			cleanup();
		} catch (emitErr) {
			console.error('❌ Falha ao emitir erro SSE no catch principal:', emitErr);
		}
	} finally {
		// Fecha o stream apenas se ainda não foi fechado
		cleanup();
	}
});

async function logSuperAIMessages({
	payloadUserToken,
	messageData,
	operationData
}: {
	payloadUserToken: string;
	messageData: any;
	operationData: any;
}) {
	const path = '/mitraspace/iaLog/';
	const { API_BASE_URL } = useRuntimeConfig().public;

	const mitraEcpAxios = axios.create({
		baseURL: API_BASE_URL as string,
		headers: {
			Accept: 'application/json',
			Authorization: payloadUserToken
		}
	});

	try {
		// Log cURL para createMessage
		const createMessageUrl = `${API_BASE_URL as string}${path}createMessage`;
		const curlCreateMessage = `curl -X POST '${createMessageUrl}' -H 'Accept: application/json' -H 'Authorization: ${payloadUserToken}' -H 'Content-Type: application/json' --data-raw '${JSON.stringify(
			messageData
		)}'`;
		console.log('🐚 cURL para createMessage:', curlCreateMessage);
		await mitraEcpAxios.post(`${path}createMessage`, messageData);

		// Log cURL para createOperation
		const createOperationUrl = `${API_BASE_URL as string}${path}createOperation`;
		const curlCreateOperation = `curl -X POST '${createOperationUrl}' -H 'Accept: application/json' -H 'Authorization: ${payloadUserToken}' -H 'Content-Type: application/json' --data-raw '${JSON.stringify(
			operationData
		)}'`;
		console.log('🐚 cURL para createOperation:', curlCreateOperation);
		await mitraEcpAxios.post(`${path}createOperation`, operationData);

		console.log('✅ Operação registrada no servidor do Mitra!');
	} catch (error) {
		console.error('❌ Falha ao registrar operação no servidor do Mitra:', error);
	}
}

// Função para processar function calls
async function processFunctionCalls(
	functionCalls: any[],
	modelParts: any[],
	ai: any,
	payload: GeminiChatPayload,
	cleanedContents: any[],
	toolConfig: any,
	event: any,
	sendChunk: (chunk: StreamChunk) => string,
	addChunkToProcess: (text: string) => void
): Promise<any> {
	console.log('🔧 Processando function calls:', functionCalls.length);

	let functionCallGroundingMetadata: any = null; // Capturar grounding metadata das function calls

	// Função real para execução de SQL (baseada no useGeminiAI.ts)
	const executeSQLFunction = async (query: string, jdbcConfigId?: number) => {
		console.log('🗄️ Executando SQL:', query);

		const finalJdbcConfigId = jdbcConfigId || payload.jdbcConfigId || 1;
		const baseUrl = payload.baseUrl || 'https://api0.mitraecp.com:1005';
		const authToken = payload.authorizationToken;

		if (!authToken) {
			return {
				success: false,
				error: 'Token de autorização não fornecido',
				query,
				jdbcConfigId: finalJdbcConfigId
			};
		}

		try {
			// Fazer a requisição para a API real
			const response = await fetch(`${baseUrl}/iaShortcuts/query`, {
				method: 'POST',
				headers: {
					Accept: 'application/json, text/plain, */*',
					Authorization: authToken,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ query, jdbcConfigId: finalJdbcConfigId })
			});
			sendChunk({
				candidates: [
					{
						content: {
							parts: [
								{
									codeExecutionResult: {
										query,
										jdbcConfigId: finalJdbcConfigId,
										result: response,
										success: response.ok
									}
								}
							]
						}
					}
				]
			});

			if (!response.ok) {
				let errorBody = null;
				let errorMessage = `HTTP ${response.status} - ${response.statusText}`;

				try {
					const errorText = await response.text();
					if (errorText) {
						try {
							errorBody = JSON.parse(errorText);
							errorMessage = errorBody.message || errorBody.error || errorMessage;
						} catch {
							errorMessage = errorText;
						}
					}
				} catch (parseError) {
					console.warn('Could not parse error response:', parseError);
				}

				return {
					success: false,
					error: errorMessage,
					statusCode: response.status,
					statusText: response.statusText,
					errorBody,
					query,
					jdbcConfigId: finalJdbcConfigId
				};
			}

			const data = await response.json();
			return {
				success: true,
				data,
				query,
				jdbcConfigId: finalJdbcConfigId
			};
		} catch (error) {
			console.error('Error executing SQL query:', error);

			let errorMessage = 'Unknown error';
			let errorDetails = null;

			if (error instanceof Error) {
				errorMessage = error.message;
				errorDetails = {
					name: error.name,
					stack: error.stack
				};
			} else if (typeof error === 'string') {
				errorMessage = error;
			} else {
				errorDetails = error;
			}

			return {
				success: false,
				error: errorMessage,
				errorDetails,
				query,
				jdbcConfigId: finalJdbcConfigId,
				errorType: 'network_or_parsing_error'
			};
		}
	};

	const executeRunAction = async (
		actionName: string,
		variables: Record<string, any>
	) => {
		console.log('🛠️ Buscando action para executar:', actionName);

		const baseUrl = payload.baseUrl || 'https://api0.mitraecp.com:1005';
		const authToken = payload.authorizationToken;

		if (!authToken) {
			return {
				success: false,
				error: 'Token de autorização não fornecido',
				actionName
			};
		}

		// Pega as ações existentes no projeto
		const actionResponse = await fetch(`${baseUrl}/iaShortcuts/action/all`, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				Authorization: authToken,
				'Content-Type': 'application/json'
			}
		});

		if (!actionResponse.ok) {
			return {
				success: false,
				error: 'Erro ao carregar lista de ações.',
				actionName
			};
		}

		const actionsList = await actionResponse.json();
		const screenId = payload.configChain?.selectedScreenId;
		const allowedActions =
			payload.configChain?.currentUserToolSchema?.CALL_ACTION || [];

		console.log('🛠️ allowedActions', allowedActions);
		console.log('🛠️ actionsList', actionsList);

		if (!Array.isArray(actionsList)) {
			return {
				success: false,
				error: 'Lista de ações não encontrada no configChain',
				actionName
			};
		}

		if (!Array.isArray(allowedActions)) {
			return {
				success: false,
				error: 'Erro ao carregar lista de ações permitidas.',
				actionName
			};
		}

		const matchedAction = actionsList.find(
			(action: any) => action?.name?.toLowerCase() === actionName?.toLowerCase()
		);

		console.log('🛠️ matchedAction', matchedAction);

		if (!matchedAction) {
			return {
				success: false,
				error: `Ação '${actionName}' não encontrada na lista de ações.`,
				actionName
			};
		}

		const isAllowed = allowedActions.some(
			(action: any) => action?.actionName?.toLowerCase() === actionName?.toLowerCase()
		);

		console.log('🛠️ isAllowed', isAllowed);

		if (!isAllowed) {
			return {
				success: false,
				error: `A ação '${actionName}' não é permitida para o usuário atual neste contexto. Verifique suas permissões ou entre em contato com o administrador.`,
				actionName
			};
		}

		const finalBody = {
			actionName: matchedAction.name,
			actionId: matchedAction.id,
			screenId,
			vars: variables
		};

		console.log('📤 Enviando chamada run_actions para backend:', finalBody);

		try {
			// montar curl
			// const endpoint = `${baseUrl}/businessAiShortcut/action/run`;
			// const headers = {
			//   Accept: 'application/json',
			//   Authorization: authToken,
			//   'Content-Type': 'application/json'
			// };
			// const body = JSON.stringify(finalBody, null, 2); // formatado para legibilidade

			// const curlCommand = [
			//   `curl -X POST "${endpoint}"`,
			//   `-H "Accept: application/json"`,
			//   `-H "Authorization: ${authToken}"`,
			//   `-H "Content-Type: application/json"`,
			//   `--data '${body}'`
			// ].join(' \\\n  ');

			// console.log('🐚 Requisição CURL equivalente:\n', curlCommand);

			const response = await fetch(`${baseUrl}/businessAiShortcut/action/run`, {
				method: 'POST',
				headers: {
					Accept: 'application/json',
					Authorization: authToken,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(finalBody)
			});

			if (!response.ok) {
				const errorText = await response.text();
				let errorBody;
				try {
					errorBody = JSON.parse(errorText);
				} catch {
					errorBody = errorText;
				}

				return {
					success: false,
					statusCode: response.status,
					error: errorBody || 'Erro ao executar ação',
					actionName: matchedAction.actionName,
					actionId: matchedAction.actionId
				};
			}

			const data = await response.json();

			return {
				success: true,
				data,
				actionName: matchedAction.actionName,
				actionId: matchedAction.actionId
			};
		} catch (error) {
			console.error('❌ Erro de rede ao executar run_actions:', error);

			return {
				success: false,
				error: error instanceof Error ? error.message : 'Erro desconhecido',
				actionName: matchedAction.actionName,
				actionId: matchedAction.actionId
			};
		}
	};

	// Processar cada function call
	const functionResponses: any[] = [];

	for (const functionCall of functionCalls) {
		if (functionCall.name === 'run_sql') {
			const args = functionCall.args as { query: string; jdbcConfigId?: number };
			const query = args?.query;
			const jdbcConfigId = args?.jdbcConfigId;

			if (query) {
				try {
					const result = await executeSQLFunction(query, jdbcConfigId);

					// Log da execução da ferramenta (não precisa enviar via stream pois será processado pelo Gemini)
					console.log('🔧 Tool execution result:', {
						functionName: functionCall.name,
						query,
						jdbcConfigId: jdbcConfigId || payload.jdbcConfigId || 1,
						success: result.success
					});

					functionResponses.push({
						functionResponse: {
							name: 'run_sql',
							response: result
						}
					});
				} catch (error) {
					console.error('Erro ao executar SQL:', error);

					// Log do erro da ferramenta
					console.error('🔧 Tool execution error:', {
						functionName: functionCall.name,
						query,
						jdbcConfigId: jdbcConfigId || payload.jdbcConfigId || 1,
						error: error instanceof Error ? error.message : 'Erro desconhecido'
					});

					// Verificar se é um erro crítico que deve interromper o streaming
					const errorMessage =
						error instanceof Error ? error.message : 'Erro desconhecido';
					const isCriticalError =
						errorMessage.includes('ECONNREFUSED') ||
						errorMessage.includes('timeout') ||
						errorMessage.includes('Network Error') ||
						errorMessage.includes('500') ||
						errorMessage.includes('502') ||
						errorMessage.includes('503') ||
						errorMessage.includes('504');

					if (isCriticalError) {
						console.error(
							'🚨 ERRO CRÍTICO detectado durante function call - interrompendo streaming'
						);
						throw new Error(`Critical error during function call: ${errorMessage}`);
					}

					functionResponses.push({
						functionResponse: {
							name: 'run_sql',
							response: {
								success: false,
								error: errorMessage
							}
						}
					});
				}
			}
		}
		if (functionCall.name === 'run_actions') {
			const args = functionCall.args as {
				actionName: string;
				variables?: Record<string, string>;
			};
			const actionName = args?.actionName;
			const variables = args?.variables || {};

			console.log(`⚙️ Executando run_actions: ${actionName}`);
			console.log('📦 Variáveis:', variables);

			try {
				const result = await executeRunAction(actionName, variables);

				functionResponses.push({
					functionResponse: {
						name: 'run_actions',
						response: result
					}
				});
			} catch (error) {
				console.error('❌ Erro ao executar run_actions:', error);

				functionResponses.push({
					functionResponse: {
						name: 'run_actions',
						response: {
							success: false,
							error: error instanceof Error ? error.message : 'Unknown error'
						}
					}
				});
			}
		}
	}

	// Se há respostas de function calls, fazer nova chamada ao Gemini
	if (functionResponses.length > 0) {
		const updatedContents = [
			...cleanedContents,
			{
				role: 'model',
				parts: modelParts
			},
			{
				role: 'user',
				parts: functionResponses
			}
		];

		console.log('🔄 Fazendo nova chamada ao Gemini com respostas das ferramentas');

		const followUpResponse = await ai.models.generateContentStream({
			model: payload.model,
			contents: updatedContents,
			config: toolConfig
		});

		// Processar resposta do follow-up - enviar response completo como o useGeminiAI.ts fazia
		for await (const response of followUpResponse) {
			// Capturar grounding metadata se disponível (LOG TODOS OS CHUNKS)
			if (response.candidates?.[0]?.groundingMetadata) {
				const newMetadata = response.candidates[0].groundingMetadata;

				console.log('🔍 GROUNDING DEBUG - CHUNK FUNCTION CALL RECEBIDO:', {
					hasGroundingSupports: !!newMetadata.groundingSupports,
					hasSearchEntryPoint: !!newMetadata.searchEntryPoint,
					hasWebSearchQueries: !!newMetadata.webSearchQueries,
					groundingSupportsCount: newMetadata.groundingSupports?.length || 0,
					webSearchQueriesCount: newMetadata.webSearchQueries?.length || 0,
					keys: Object.keys(newMetadata)
				});

				// SEMPRE substitui para ver todos os chunks
				functionCallGroundingMetadata = newMetadata;
				console.log('🔍 GROUNDING DEBUG - Metadata atualizado (function call)');
			}

			// Enviar a resposta completa no formato original do Gemini (como era no useGeminiAI.ts)
			sendChunk(response);

			// Processar function calls e chunks de texto para execução recursiva no server-side
			if (response.candidates?.[0]?.content?.parts) {
				for (const part of response.candidates[0].content.parts) {
					if (part.functionCall) {
						console.log('🔄 Detectados mais function calls, processando recursivamente...');
						const recursiveMetadata = await processFunctionCalls(
							[part.functionCall],
							[part],
							ai,
							payload,
							updatedContents,
							toolConfig,
							event,
							sendChunk,
							addChunkToProcess
						);

						// Se chamada recursiva retornou grounding metadata mais completo, usar ele
						if (
							recursiveMetadata &&
							(!functionCallGroundingMetadata || recursiveMetadata.groundingSupports)
						) {
							functionCallGroundingMetadata = recursiveMetadata;
							console.log('🔍 GROUNDING DEBUG - Metadata recursivo usado');
						}
					} else if (part.text) {
						// Adicionar chunk de texto ao processamento throttled (igual ao useGeminiAI.ts)
						addChunkToProcess(part.text);
					}
				}
			}
		}
	}

	// Retornar grounding metadata capturado durante function calls
	return functionCallGroundingMetadata;
}
