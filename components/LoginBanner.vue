<template>
	<div
		class="login-banner"
		:style="{
			backgroundImage:
				!!whiteLabelApp && hasLoginBanner && !byMitra
					? `url(${whiteLabelApp.loginBannerImg})`
					: `url(${MitraBanner})`
		}"
	>
		<div
			v-if="byMitra"
			class="flex flex-col items-center justify-center text-white p-8"
		>
			<img
				src="assets/images/mockups/interface_model.png"
				alt="Interface model"
			/>
			<span class="text-3xl font-semibold leading-[38px] my-4 text-center text-white">
				{{ $t('LOGIN.BANNER.title') }}
			</span>
			<div class="flex flex-wrap gap-x-6 gap-y-2 max-w-[547px]">
				<div
					v-for="feature in features"
					:key="feature.text"
					class="flex items-center"
				>
					<HugeiconsIcon
						:icon="feature.icon"
						class="text-white mr-3"
						size="24"
					/>
					<span class="text-lg font-normal leading-7 text-white/90">{{
						$t(feature.text)
					}}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { HugeiconsIcon } from '@hugeicons/vue';
	import {
		CloudUploadIcon,
		DatabaseIcon,
		DatabaseSyncIcon,
		DashboardSquareAddIcon,
		UserShield01Icon
	} from '@hugeicons-pro/core-stroke-rounded';
	import MitraBanner from 'assets/images/backgrounds/universe_banner.png';

	const features = [
		{ text: 'LOGIN.BANNER.FEATURES.publish', icon: CloudUploadIcon },
		{ text: 'LOGIN.BANNER.FEATURES.database', icon: DatabaseIcon },
		{ text: 'LOGIN.BANNER.FEATURES.integrate', icon: DatabaseSyncIcon },
		{ text: 'LOGIN.BANNER.FEATURES.components', icon: DashboardSquareAddIcon },
		{ text: 'LOGIN.BANNER.FEATURES.auth', icon: UserShield01Icon }
	];

	const { whiteLabelApp } = useMarketplacePublisher();

	const hasLoginBanner = computed(() => {
		return !!whiteLabelApp?.loginBannerImg;
	});

	defineProps<{
		byMitra?: boolean;
	}>();
</script>

<style scoped src="assets/css/shared/login.css"></style>
