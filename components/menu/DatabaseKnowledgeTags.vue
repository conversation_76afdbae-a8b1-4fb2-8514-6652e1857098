<template>
	<a-card>
		<div class="tags-container">
			<span class="tags-title"> Tags: </span>

			<div
				class="tags-add"
				@click="createNewTag"
			>
				<HugeiconsIcon
					:icon="Add01Icon"
					size="16"
					color="var(--grey-600)"
					class="shrink-0"
				></HugeiconsIcon>
				<span class="tags-add-text"> Criar Nova Tag </span>
			</div>

			<div class="tags-container-list">
				<div
					v-for="tag in tags"
					:key="tag.name"
					class="tag-wrapper"
				>
					<a-tag
						v-if="tag.id"
						:color="tag.color"
						class="tag-label"
					>
						{{ tag.name }}
					</a-tag>

					<div
						v-else
						class="tag-input"
					>
						<a-input
							v-model:value="addTagName"
							placeholder="Nome da Tag"
							@blur="saveTag(tag)"
						/>
					</div>

					<a-popover
						overlay-class-name="database-menu"
						placement="rightBottom"
						trigger="click"
						@visibleChange="editingTagName = tag.name"
					>
						<a class="tag-actions">
							<more-outlined />
						</a>

						<template #content>
							<a-card class="edit-modal">
								<a-input
									v-model:value="editingTagName"
									placeholder="Nome da Tag"
									@blur="updateName(tag)"
								/>

								<a-button
									type="text"
									danger
									class="delete-btn"
									@click="deleteCurrentTag(tag)"
								>
									<HugeiconsIcon
										:icon="Delete01Icon"
										color="var(--grey-500)"
										class="mr-1"
										:size="18"
									/>
									Deletar
								</a-button>

								<div class="color-list">
									<div
										v-for="color in colors"
										:key="color.value"
										class="color-item"
										@click="updateColor(tag, color)"
									>
										<div
											class="color-box"
											:style="{
												backgroundColor: color.hex,
												border:
													tag.color === color.value ? '1px solid #000' : ''
											}"
										></div>
										<span>{{ color.label }}</span>
									</div>
								</div>
							</a-card>
						</template>
					</a-popover>
				</div>
			</div>
		</div>

		<a-button
			type="text"
			danger
			class="remove-base"
			@click="removeBase"
		>
			<HugeiconsIcon
				:icon="CancelCircleHalfDotIcon"
				color="var(--grey-500)"
				class="mr-1"
				:size="18"
			/>
			<span class="remove-base-text"> 
				{{ $t('GLOBAL.remove_knowledge_base_title') }}	
			</span>
		</a-button>
	</a-card>
</template>

<script setup lang="ts">
	import {
		CancelCircleHalfDotIcon,
		Delete01Icon
	} from '@hugeicons-pro/core-stroke-rounded';
	import { Add01Icon } from '@hugeicons-pro/core-solid-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { MoreOutlined } from '@ant-design/icons-vue';

	const tags = ref<
		{
			id: number | null;
			name: string;
			color: string;
			createdAt: Date | null;
			createdBy: string | null;
			updatedAt: Date | null;
			updatedBy: string | null;
		}[]
	>([]);

	const colors = [
		{ label: 'Cinza', value: 'gray', hex: '#e0e0e0' },
		{ label: 'Amarelo', value: 'yellow', hex: '#ffe58f' },
		{ label: 'Verde', value: 'green', hex: '#b7eb8f' },
		{ label: 'Azul', value: 'blue', hex: '#91d5ff' },
		{ label: 'Roxo', value: 'purple', hex: '#d3adf7' },
		{ label: 'Rosa', value: 'pink', hex: '#ffadd2' },
		{ label: 'Vermelho', value: 'red', hex: '#ff7875' }
	];

	const editingTagName = ref('');
	const addTagName = ref('');

	const createNewTag = () => {
		tags.value.unshift({
			name: '',
			color: 'gray',
			id: null,
			createdAt: null,
			createdBy: null,
			updatedAt: null,
			updatedBy: null
		});
	};

	const removeBase = () => {
		useDialogStore().switchDialog('knowledgeBaseController', true);
	};

	loadTagList();

	async function loadTagList() {
		const { data } = await useLegacyService().findAllTagsPaginated();
		tags.value = data.tags;
	}

	async function saveTag(tag: any) {
		const payload = {
			name: addTagName.value,
			color: tag.color
		};
		await useLegacyService().createTag(payload);
		addTagName.value = '';
		loadTagList();
	}

	async function updateColor(tag: any, color: any) {
		tag.color = color.value;
		await useLegacyService().changeTagColor(tag.id, tag.color);
	}

	async function updateName(tag: any) {
		await useLegacyService().changeTagName(
			tag.id,
			editingTagName.value
		);
		editingTagName.value = '';
		loadTagList();
	}

	async function deleteCurrentTag(tag: any) {
		await useLegacyService().deleteTag(tag.id);
		loadTagList();
	};
	
</script>

<style scoped lang="postcss">
	.knowledge {
		margin: 16px 16px 0;
		display: flex;
		padding: 4px 12px;
		align-items: center;
		align-self: stretch;
		border-radius: 1000px;
		border: 1px solid var(--Gray-300, #c7cbdd);
		background: var(--Base-White, #fff);
		box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
		cursor: pointer;

		&_text {
			color: var(--Gray-700, #303854);
			font-family: Inter;
			font-size: 14px;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
		}
	}
	.tags-container {
		display: flex;
		flex-direction: column;
		gap: 10px;
		padding: 16px;
		&-list {
			border-radius: 8px;
			border: 1px solid var(--Gray-300, #c7cbdd);
			display: flex;
			height: 182px;
			padding: 6px 0;
			flex-direction: column;
			align-items: flex-start;
			align-self: stretch;
			overflow-y: auto;
		}
	}
	.tag {
		&-wrapper {
			display: flex;
			padding: 6px 12px;
			justify-content: space-between;
			align-items: center;
			align-self: stretch;
		}
		&-label {
			margin: 0;
			display: flex;
			padding: 2px 8px;
			justify-content: center;
			align-items: center;
			gap: 6px;
			border-radius: 100px;
		}
		&-actions {
			color: #888;
			cursor: pointer;
		}
	}
	.tags-title {
		color: var(--Gray-900, #0e1428);
		font-family: Inter;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 20px;
	}
	.tags-add {
		display: flex;
		padding: 4px 8px;
		justify-content: center;
		align-items: center;
		gap: 8px;
		align-self: stretch;
		border-radius: 8px;
		border: 1px solid var(--Gray-300, #c7cbdd);
		background: var(--Base-White, #fff);
		box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
		cursor: pointer;

		&-text {
			color: var(--Gray-700, #303854);
			font-family: Inter;
			font-size: 14px;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
		}
	}

	.remove-base {
		display: flex;
		align-items: center;
		margin-bottom: 10px;

		&-text {
			color: var(--Gray-900, #0e1428);
			font-family: Inter;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 20px;
		}
	}
	</style>

	<style scoped>
	.ant-select-item-option:has(.disabledJdbc) {
		pointer-events: none;
	}

	.tag-input {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 6px;
		width: 140px;
	}

	.tag-label {
		margin: 0;
	}

	.tag-actions {
		color: #888;
		cursor: pointer;
	}

	.delete-btn {
		display: flex;
		align-items: center;
		background: #f9f9f9;
		border-radius: 8px;
	}

	.color-list {
		display: flex;
		flex-direction: column;
		gap: 10px;
		margin-top: 16px;
	}

	.color-item {
		display: flex;
		align-items: center;
		gap: 10px;
		cursor: pointer;
	}

	.color-box {
		width: 20px;
		height: 20px;
		border-radius: 4px;
	}

	.edit-modal {
		display: flex;
		width: 248px;
		padding: 16px;
		flex-direction: column;
		align-items: flex-start;
		gap: 8px;

		border-radius: 6px;
		background: var(--Base-White, #fff);

		box-shadow: 0 4px 8px -2px rgba(16, 24, 40, 0.1),
			0 2px 4px -2px rgba(16, 24, 40, 0.06);
	}
</style>
