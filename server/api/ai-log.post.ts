import { defineEventHand<PERSON>, readBody } from 'h3';
import axios from 'axios';

// Mantendo as interfaces para clareza
interface DataAILogMessage {
	[key: string]: any;
}

interface DataAIOperation {
	[key: string]: any;
}

interface GeminiApiKeyConfig {
	isToUseUserIaCredit: boolean;
	isToUseOwnKey: boolean;
	workspaceId: number;
	freeIaCreditsUsage: number;
	isSankhya: boolean;
}

interface RequestBody {
	messageData: DataAILogMessage;
	operationData: DataAIOperation;
	spaceToken: string;
	url: string;
	geminiApiKeyConfig: GeminiApiKeyConfig;
}

export default defineEventHandler(async (event) => {
	try {
		const body: RequestBody = await readBody(event);
		const { API_BASE_URL } = useRuntimeConfig().public;

		// Validação dos dados de entrada
		if (!body.messageData || !body.operationData) {
			event.node.res.statusCode = 400;
			return { error: 'Os campos messageData e operationData são obrigatórios.' };
		}

		if (!body.spaceToken) {
			event.node.res.statusCode = 401;
			return { error: 'Cabeçalho de autorização não encontrado.' };
		}

		const axiosInstance = axios.create({
			baseURL: API_BASE_URL,
			headers: {
				Accept: 'application/json',
				Authorization: body.spaceToken
			}
		});

		// Criação dos cabeçalhos para as requisições internas
		const { geminiApiKeyConfig } = body;

		const apiKey = await getGeminiApiKey(
			API_BASE_URL,
			body.spaceToken,
			geminiApiKeyConfig.isToUseUserIaCredit,
			geminiApiKeyConfig.isToUseOwnKey,
			geminiApiKeyConfig.workspaceId,
			geminiApiKeyConfig.freeIaCreditsUsage,
			geminiApiKeyConfig.isSankhya
		);

		body.messageData.keyUsed = apiKey;

		// Executa as duas operações em sequência, repassando a autorização
		// eslint-disable-next-line no-console
		console.log(`
--- LOG da Requisição para /mitraspace/iaLog/createMessage ---
curl -X POST '${API_BASE_URL}/mitraspace/iaLog/createMessage' \\
		-H 'Authorization: ${body.spaceToken}' \\
		-H 'Content-Type: application/json' \\
		-d '${JSON.stringify(body.messageData, null, 2)}'
-------------------------------------------------------------
`);
		const messageResult = await axiosInstance.post(
			'/mitraspace/iaLog/createMessage',
			body.messageData
		);

		// eslint-disable-next-line no-console
		console.log(`
--- LOG da Requisição para /mitraspace/iaLog/createOperation ---
curl -X POST '${API_BASE_URL}/mitraspace/iaLog/createOperation' \\
		-H 'Authorization: ${body.spaceToken}' \\
		-H 'Content-Type: application/json' \\
		-d '${JSON.stringify(body.operationData, null, 2)}'
-------------------------------------------------------------
`);
		const operationResult = await axiosInstance.post(
			'/mitraspace/iaLog/createOperation',
			body.operationData
		);

		// Retorna uma resposta de sucesso
		// Garante que estamos retornando um objeto JSON puro para evitar referências circulares
		const messageResponseData = JSON.parse(JSON.stringify(messageResult.data));
		const operationResponseData = JSON.parse(JSON.stringify(operationResult.data));

		return {
			message: 'Operações de log de IA executadas com sucesso.',
			newMessageId: messageResponseData,
			newOperationData: operationResponseData
		};
	} catch (error: any) {
		// Define o status code do erro, se disponível, senão 500
		event.node.res.statusCode = error.response?.status || error.statusCode || 500;

		// Extrai e limpa os detalhes do erro para evitar referências circulares
		let details = error.message;
		if (error.response && error.response.data) {
			try {
				details = JSON.parse(JSON.stringify(error.response.data));
			} catch {
				details = 'Erro não serializável na resposta da API.';
			}
		}

		// Retorna uma mensagem de erro detalhada
		return {
			error: 'Ocorreu um erro ao processar sua solicitação de log de IA.',
			details
		};
	}
});
