<template>
	<MitraButton
		v-if="showButton"
		:text="$t('GLOBAL.new_project_without_prompt')"
		outlined
		color="var(--white)"
		text-color="var(--grey-700)"
		:icon="mdiPlus"
		data-cy="add-project-button"
		icon-class="clean-svg"
		:class="{
			'whitelabel-btn': whiteLabelApp?.nameProject === 'Zaya'
		}"
		class="z-[2] !shadow-none"
		@click="openCreateProjectModal"
	/>
</template>

<script setup>
import { mdiPlus } from '@mdi/js';
import { useCreateProject } from '~/composables/useCreateProject';

const { openCreateProjectModal } = useCreateProject();
const { readonlyWorkspace, selectedWorkspace } = storeToRefs(useWorkspaceStore());
const { whiteLabelApp } = storeToRefs(useMarketplacePublisher());
const sankhyaStore = useSankhyaStore();

const showButton = computed(() => {
	return (
		(readonlyWorkspace.value && selectedWorkspace.value?.canCreateProjects) ||
		!readonlyWorkspace.value ||
		sankhyaStore.isAdminOrMasterUser()
	);
});
</script>