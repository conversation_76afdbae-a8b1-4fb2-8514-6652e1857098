{"version": "0.2.0", "configurations": [{"type": "node-terminal", "request": "launch", "name": "Debug Sankhya", "command": "npm run dev:sk"}, {"type": "node-terminal", "request": "launch", "name": "Debug Validacao", "command": "npm run dev:valid<PERSON><PERSON>"}, {"type": "node-terminal", "request": "launch", "name": "Debug Preview", "command": "npm run dev:preview"}, {"type": "node-terminal", "request": "launch", "name": "Debug Validacao Android", "command": "npm run dev:android"}]}