export default defineNuxtRouteMiddleware((to) => {
	const options: any = {};
	if (useWorkspaceStore().publicScreenId) {
		options.sessionId = useWorkspaceStore().sessionId;
		options.tenantId = useWorkspaceStore().selectedProject.id;
	}

	const payload = Object.entries(to.query)
		.filter(([key]) => key.startsWith('VAR_'))
		.map(([key, value]) => ({
			name: `:${key}`,
			content: value
		}));

	useConnectionService()
		.setVariableList(payload, options)
		.then(() => {});
});
