.ant-modal {
	&-content {
		box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
			0px 2px 4px -2px rgba(16, 24, 40, 0.06);
		@apply rounded-2xl px-4 py-5;
	}
	&-header {
		border-bottom: none;
		@apply light-border-b h-[40px] p-0;
	}
	&-body {
		@apply p-0;
		> div:first-child {
			@apply rounded-xl;
		}
	}
	&-title {
		@apply font-medium text-grey-800;
	}
	&-close-x {
		svg {
			@apply h-[10px] w-[12px] fill-[var(--grey-400)];
		}
	}
}

.ant-radio-checked {
	&:hover {
		.ant-radio-inner {
			border-color: var(--violet-600);
		}
	}
	&::after {
		border: 1px solid var(--violet-600);
	}
	.ant-radio-inner {
		border-color: var(--violet-600);
		&::after {
			background-color: var(--violet-600);
		}
	}
}

.ant-radio-input:focus + .ant-radio-inner {
	box-shadow: 0 0 0 3px var(--violet-200);
}

.ant-radio-wrapper:hover .ant-radio,
.ant-radio:hover .ant-radio-inner,
.ant-radio-input:focus + .ant-radio-inner {
	border-color: var(--violet-700);
}

.notice-modal {
	.ant-modal-content {
		padding: 24px;
		border-radius: 12px;
	}
}

.input--no-error {
	.ant-form-item-explain {
		display: none !important;
	}
}

.skeleton-loading {
	.ant-select-selector {
		@apply animate-pulse;
		border: none !important;
		background-color: var(--grey-200) !important;
		* {
			display: none;
		}
	}
	.ant-switch {
		background-color: var(--grey-200) !important;
		@apply animate-pulse;
		* {
			display: none;
		}
		.skw-app .ant-switch-checked {
			background-color: var(--grey-200) !important;
		}
	}
	.ant-select-arrow {
		display: none;
	}
	.text-skeleton {
		width: 100px;
		height: 18px;
		background-color: var(--grey-200) !important;
		@apply animate-pulse;
		border-radius: 6px;
	}
}

.studio-modal {
	.ant-modal-content {
		border-radius: 16px;
		border: 1px solid var(--grey-200);
		box-shadow: 0px 1px 3px 0px rgba(16, 24, 40, 0.1),
			0px 1px 2px 0px rgba(16, 24, 40, 0.06);
		padding: 20px 0;
	}
	.ant-modal-header {
		padding: 0 24px;
	}
	.ant-modal-footer {
		padding: 0 24px;
	}
}

.security-corp-modal {
	.ant-modal-header {
		height: 70px !important;
		.rounded-icon-button {
			top: 13px !important;
		}
	}
}

.security-corp-modal-config {
	.ant-modal-header {
		height: 50px !important;
		.rounded-icon-button {
			top: 4px !important;
		}
	}
}

.coupon-success-modal {
	.ant-modal-content {
		padding: 32px 40px !important;
		.ant-modal-close-icon {
			> svg {
				@apply h-[14px] w-[14px] fill-[var(--grey-500)];
			}
		}
	}
}

.ft-modal {
	.ant-modal-content {
		border-radius: 16px;
		padding: 0 !important;
	}
}

.ft-modal.ant-popover {
	.ant-popover-inner-content {
		border-radius: 16px;
		padding: 0 !important;
	}
}

.fast-trak-into-db {
	.rounded-icon-button {
		position: unset !important;
		margin-bottom: 4px !important;
	}
	.ant-modal-content {
		border-radius: 16px;
		padding: 0 !important;
		.ant-modal-close-icon {
			> svg {
				@apply h-[14px] w-[14px] fill-[var(--grey-500)];
			}
		}
	}
}

.alert-limit-modal {
	.rounded-icon-button {
		position: unset !important;
		margin-bottom: 4px !important;
	}
	.ant-modal-content {
		border-radius: 16px;
		padding: 0 !important;
		.ant-modal-close-icon {
			> svg {
				@apply h-[14px] w-[14px] fill-[var(--grey-500)];
			}
		}
	}
}

.base-mitra-modal {
	&--no-padding {
		.ant-modal-body {
			padding: 0 !important;
			.button-wrapper {
				height: unset !important;
			}
		}
	}
	&__flat {
		.ant-modal {
			&-header {
				/* height: 52px !important; */
				height: unset !important;
				border-radius: 4px;
			}
			.ant-modal-content {
				padding: 0 !important;
				border-radius: 8px;
			}
			.ant-modal-header,
			.ant-modal-footer {
				padding: 16px;
				.button-wrapper {
					height: 44px;
					/* width: 105px; */
				}
			}
			.ant-modal-header {
				border-top-left-radius: 16px;
				border-top-right-radius: 16px;
			}

			.ant-modal-footer {
				padding: 16px 24px !important;
			}

			.ant-modal-body {
				padding: 0 !important;
			}

			.ant-modal-footer {
				padding: 8px 16px;
			}
		}
	}
	.ant-modal {
		&-header {
			/* height: 52px !important; */
			height: unset !important;
			border-radius: 4px;
		}
		.ant-modal-content {
			padding: 0 !important;
			border-radius: 4px;
		}
		.ant-modal-header,
		.ant-modal-body,
		.ant-modal-footer {
			padding: 16px;
			.button-wrapper {
				height: 36px;
			}
		}

		.ant-modal-footer {
			padding: 8px 16px;
		}
	}
}

.new-timeline-modal {
	.ant-modal {
		&-header {
			/* height: 52px !important; */
			height: unset !important;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
		}
		.ant-modal-content {
			padding: 0 !important;
			border-radius: 16px;
		}
		.ant-modal-header,
		.ant-modal-body,
		.ant-modal-footer {
			padding: 0px;
		}

		.ant-modal-footer {
			padding: 0px;
		}
	}
}

.base-mitra-modal-connection {
	.ant-modal {
		&-header {
			/* height: 52px !important; */
			height: unset !important;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
		}
		.ant-modal-content {
			padding: 0 !important;
			border-radius: 10px;
		}
		.ant-modal-header,
		.ant-modal-body,
		.ant-modal-footer {
			padding: 0px;
		}

		.ant-modal-footer {
			padding: 0px;
		}
	}
}

.create-jdbc-modal {
	max-height: 100vh;
	.ant-modal-content {
		padding: 0px;
	}
}
.marktplace-modal {
	max-height: 100vh;
	.ant-modal-content {
		padding: 0px;
		border-radius: 12px;
		border: 1px solid var(--grey-25);
		box-shadow: 0px 1px 3px 0px rgba(16, 24, 40, 0.1),
			0px 1px 2px 0px rgba(16, 24, 40, 0.06);
	}
}

.full-height-modal {
	max-height: 100vh;
	border-radius: 14px;
	.ant-modal-header {
		padding-left: 10px;
		border-top-left-radius: 14px;
		border-top-right-radius: 14px;
		height: 70px;
		padding-top: 20px;
		background: white;
	}
	.ant-modal-content {
		background: #f8f9fd;
		padding: 0px;
		height: calc(100vh - 40px);
	}
}

.iframe-modal {
	max-height: 100vh;
	.ant-modal-header {
		margin: 10px 0px 10px 0 !important;
		padding-left: 10px;
		height: 40px;
	}
	.ant-modal-content {
		padding: 0px;
		padding-top: 5px;
		height: calc(100vh - 40px);
	}
}

.byok-modal-mobile {
	max-height: 150vh;
	.ant-modal-content {
		padding: 16px;
		height: 100%;
	}
}

.byok-modal {
	max-height: 100vh;
	.ant-modal-content {
		padding: 0px;
		height: 100%;
	}
}

.byok-modal-mobile {
	max-height: 150vh;
	.ant-modal-content {
		padding: 16px;
		height: 100%;
	}
}

.profile-out-modal {
	max-height: 100vh;
	.ant-modal-content {
		padding: 0px;
		height: 100%;
	}
}

.connection-template-modal {
	.ant-modal-content {
		padding: 0px;
	}
}

.ant-checkbox {
	&-inner {
		border-color: var(--violet-600) !important;
		background-color: var(--violet-50) !important;
		border-radius: 4px !important;
	}
}

.ant-switch {
	background-color: var(--grey-100);
	min-width: 36px;
	min-height: 20px;
	height: 20px;
	width: 36px;

	&-checked {
		background-color: var(--violet-500);
	}

	&:focus {
		box-shadow: none;
		border: none;
	}

	&:not(.ant-switch-checked):hover {
		background-color: var(--grey-200);
	}

	&.ant-switch-checked:hover {
		background-color: var(--violet-600);
	}

	&-handle {
		width: 16px;
		height: 16px;
		box-shadow: var(--shadow-sm);
		border-radius: 9px;
	}
}

.ant-switch:not(.ant-switch-disabled):active.ant-switch-checked
	.ant-switch-handle::before {
	right: 0;
	left: 0%;
}

.ant-checkbox-checked {
	.ant-checkbox & .ant-checkbox-inner {
		&::after {
			position: absolute;
			display: table;
			border: 2px solid var(--violet-600);
			border-top: 0;
			border-left: 0;
			transform: rotate(45deg) scale(1) translate(-50%, -50%);
			opacity: 1;
			transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
			content: ' ';
		}
	}
	&::after {
		border: 1px solid var(--violet-600) !important;
		border-radius: 4px !important;
	}
}

.ant-input {
	&::placeholder {
		font-size: 14px;
		color: var(--grey-500) !important;
	}
	&:focus {
		border-color: var(--violet-400);
		box-shadow: theme(boxShadow.violet);
	}
	&:hover {
		border-color: var(--violet-400);
	}
}

.view-popover {
	.ant-popover-inner-content {
		padding-top: 4px;
	}
}

.base-autocomplete-popover {
	.ant-popover-inner-content {
		@apply shadow-md;
		padding: 0px;
		border-radius: 4px;
		border: 1px solid var(--grey-25);
		background: white;
	}
}

.connection-mapper-popover {
	.ant-popover-inner-content {
		@apply shadow-lg;
		padding: 0px;
		border-radius: 6px;
		border: 1px solid var(--grey-25);
		background: white;
	}
}

.query-notification-popover {
	.ant-popover-inner-content {
		@apply shadow-md;
		padding: 0px;
		border-radius: 4px;
		border: 1px solid var(--grey-25);
		background: white;
	}
}

.view-tabs {
	.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--violet-600);
		text-shadow: none;
	}

	.ant-tabs-nav {
		height: 36px;
		margin-bottom: 16px;
		&::before {
			border: none;
		}

		.ant-tabs {
			&-ink-bar {
				background-color: var(--violet-600);
			}
			&-tab {
				color: var(--violet-600);
				padding: 8px 0;
				width: 107px;
				justify-content: center;
				font-weight: 500;
				&-btn {
					color: var(--grey-600);
				}
				&:hover {
					color: var(--violet-600);
				}
			}
		}
	}
}

.project-tabs {
	> .ant-tabs-nav {
		margin-bottom: 4px !important;
	}
	.ant-tabs-content-holder {
		overflow: hidden !important;
	}
	.ant-tabs-content {
		height: 100%;
		overflow: hidden !important;
	}
	.ant-tabs-tabpane {
		overflow: hidden !important;
	}
}

.filter-variables-tabs,
.sankhya-licenses-tabs {
	height: -webkit-fill-available;

	.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--violet-600);
		text-shadow: none;
	}

	.ant-tabs-content-holder {
		display: flex !important;
	}

	.ant-tabs-tabpane.ant-tabs-tabpane-active {
		height: -webkit-fill-available;
		display: flex;
		flex-direction: column;
	}

	.ant-tabs-nav {
		max-height: 29px;
		margin-bottom: 8px;
		&::before {
			border: none;
		}
		&-list {
			width: 100%;
		}
		.ant-tabs {
			&-ink-bar {
				background-color: var(--violet-600);
			}
			&-tab {
				max-height: 29px;
				color: var(--violet-600);
				padding: 0 4px 8px 4px;
				margin: 0 8px 0 0;
				justify-content: center;
				font-weight: 500;
				&-btn {
					color: var(--grey-600);
				}
				&:hover {
					color: var(--violet-600);
				}
			}
		}
	}
}

.database-variables-tabs {
	height: -webkit-fill-available;

	.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--violet-700);
		font-size: 14px;
		font-weight: 500;
		text-shadow: none;
	}
	.ant-tabs-tab:not(.ant-tabs-tab-active) .ant-tabs-tab-btn {
		color: var(--grey-500);
		font-size: 14px;
		font-weight: 400;
	}

	.ant-tabs-content-holder {
		display: flex !important;
	}

	.ant-tabs-tabpane.ant-tabs-tabpane-active {
		height: -webkit-fill-available;
		display: flex;
		flex-direction: column;
	}

	.ant-tabs-nav {
		height: 24px;
		margin-bottom: 16px;
		display: flex;
		justify-content: flex-center; /* Alinha os tabs ao início */
		gap: 0; /* Remove espaçamento entre os tabs */

		.ant-tabs {
			&-ink-bar {
				background-color: var(--violet-600);
			}
			&-tab {
				color: var(--violet-600);
				padding: 8px 0;
				width: auto; /* Remova o tamanho fixo */
				justify-content: center;
				font-weight: 500;
				&-btn {
					color: var(--grey-600);
				}
				&:hover {
					color: var(--violet-600);
				}
			}
		}
	}
	.ant-tabs-tab + .ant-tabs-tab {
		margin-left: 16px;
	}
}

.simple-name-modal {
	.ant-popover-inner-content {
		padding: 0px;
	}
	width: 400px;
	height: 202px !important;
}
.popover-with-no-padding {
	position: absolute !important;
	.ant-popover-inner-content {
		padding: 0px;
	}
}

.database-variables-tabs-centered {
	.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--violet-700);
		font-size: 14px;
		font-weight: 500;
		text-shadow: none;
	}
	.ant-tabs-tab:not(.ant-tabs-tab-active) .ant-tabs-tab-btn {
		color: var(--grey-500);
		font-size: 14px;
		font-weight: 400;
	}

	.ant-tabs-nav {
		height: 40px;
		margin-bottom: 16px;
		padding-left: 16px;
		padding-right: 16px;
		display: flex;
		justify-content: flex-center; /* Alinha os tabs ao início */
		gap: 0; /* Remove espaçamento entre os tabs */

		.ant-tabs {
			&-ink-bar {
				background-color: var(--violet-600);
			}
			&-tab {
				color: var(--violet-600);
				padding: 8px 0;
				width: auto; /* Remova o tamanho fixo */
				justify-content: center;
				font-weight: 500;
				&-btn {
					color: var(--grey-600);
				}
				&:hover {
					color: var(--violet-600);
				}
			}
		}
	}
}

.sankhya-licenses-tabs {
	height: 43px !important;
	min-height: 43px !important;
	.ant-tabs {
		&-content-holder {
			display: none !important;
		}
	}
	user-select: none;
	.ant-tabs-nav {
		.ant-tabs-tab-btn {
			font-size: 14px;
			color: var(--grey-500);
		}
		&-wrap {
			border-bottom: 1px solid var(--grey-200);
		}
		margin-bottom: 0;
		margin-top: 6px;
		/* padding: 0 10px; */
		.ant-tabs-tab {
			width: auto !important;
			margin-left: 8px !important;
			padding: 0 8px;
		}
	}
}

.edit-data-loader-menu {
	.ant-popover-inner-content {
		padding: 0px !important;
		margin: 0px !important;
	}
	border-radius: 8px;
	border: 1px solid var(--grey-1000);

	width: 400px;

	/* Shadow/md */
	box-shadow: 0 4px 8px -2px rgba(16, 24, 40, 0.1),
		0 2px 4px -2px rgba(16, 24, 40, 0.06);

	margin: 0px !important;
	padding: 0px !important;
}

.jdbc-menu {
	.ant-popover-inner-content {
		padding: 16px !important;
		margin: 0px !important;
	}
	border-radius: 8px;
	border: 1px solid var(--grey-1000);

	width: 300px;

	/* Shadow/md */
	box-shadow: 0 4px 8px -2px rgba(16, 24, 40, 0.1),
		0 2px 4px -2px rgba(16, 24, 40, 0.06);

	margin: 0px !important;
	padding: 0px !important;
}

.members-tabs,
.custom-navigation-tabs {
	.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--primary-700) !important;
		text-shadow: none;
	}

	.ant-tabs-tab.ant-tabs-tab .ant-tabs-tab-btn {
		color: var(--grey-500);
		text-shadow: none;
	}

	.ant-tabs-content-holder {
		display: flex !important;
	}

	.ant-tabs-tabpane-active {
		display: flex;
	}

	.ant-tabs-tab-active {
		background-color: var(--primary-50);
	}

	.ant-tabs-tab + .ant-tabs-tab {
		margin: 0 0 0 12px;
	}

	.ant-tabs-nav {
		height: 36px;
		margin: 20px 0 !important;
		&::before {
			border: none;
		}

		.ant-tabs {
			&-ink-bar {
				background: transparent !important;
			}
			&-tab {
				color: var(--violet-600);
				border-radius: 6px;
				padding: 8px 12px;
				justify-content: center;
				font-weight: 500;
				&-btn {
					color: var(--primary-700);
				}
				&:not(.ant-tabs-tab-active):hover {
					color: var(--primary-700);
					background-color: var(--grey-50);
					.ant-tabs-tab-btn {
						color: var(--primary-700);
					}
				}
			}
		}
	}
}

.auth-card .ant-input {
	&::placeholder {
		color: var(--grey-500) !important;
	}
	color: var(--grey-900) !important;
	font-size: 14px;
}

/* .sheet-to-db-axis-x .ant-select-selector {
	height: 44px !important;
} */

.ant-input.local-overrided {
	width: 100%;
	height: 44px;
	background: #ffffff;
	border: 1px solid var(--grey-300);
	border-radius: 8px;
	&::placeholder {
		color: #667085;
	}
}

.ant-table-thead
	> tr
	> th:not(:last-child):not(.ant-table-selection-column):not(
		.ant-table-row-expand-icon-cell
	):not([colspan])::before {
	height: 100%;
	background: var(--grey-300);
}
.ant-table-thead > tr > th {
	border-radius: 8px !important;
}

.ant-table-thead > tr > th,
.ant-table-tbody > tr > td,
.ant-table tfoot > tr > th,
.ant-table tfoot > tr > td {
	padding: 10px 10px;
}

.ant-table {
	border: 1px solid var(--grey-300);
	border-radius: 8px;
}

/* .ant-table-cell-scrollbar {
	background: red;
} */

.ant-table-header {
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
}

.ant-table-tbody > tr > td {
	border-top: 1px solid var(--grey-300);
	border-bottom: unset;
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
	border-color: var(--violet-400);
}

.dropdown-absolute {
	width: 20px;
	height: 20px;
	right: 25px;
	cursor: pointer;
	top: 38px;
	position: absolute;
}

.ant-select-dropdown {
	.ant-select-item-option-state {
		svg {
			color: var(--grey-500);
		}
	}
}

.ant-select.multiselector-tagged {
	.ant-select-selector {
		/* padding: 0 14px !important; */
	}
	.ant-select-selection-item {
		> span {
			color: var(--grey-700);
			height: unset;
			text-align: center;
			font-family: Inter;
			font-size: 14px;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
		}
		&-remove svg {
			font-size: 10px;
			color: var(--grey-400);
		}
		height: 24px;
		margin-top: 0 !important;
		margin-inline-end: 6px;
		margin-bottom: 0 !important;
		border-radius: 100px !important;
		border: 1px solid var(--grey-300);
		background-color: white;
	}
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(
		.ant-select-customize-input
	)
	.ant-select-selector {
	border-color: var(--violet-400) !important;
	box-shadow: theme(boxShadow.violet);
}

.ant-input-affix-wrapper,
.ant-input,
.ant-select-selector {
	@apply !rounded-md;
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
	border-color: var(--violet-400);
	box-shadow: theme(boxShadow.violet);
}

.has-custom-header {
	.ant-input-affix-wrapper:focus,
	.ant-input-affix-wrapper-focused {
		box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px #f2f4f7;
	}
}

.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
	border-color: var(--violet-400);
}

.ant-form-item-extra {
	font-family: Inter;
	font-size: 14px;
	font-weight: 400;
	line-height: 20px;
	text-align: left;
	color: var(--grey-500);
}
.ant-form-extra-error {
	& .ant-form-item-extra {
		font-family: Inter;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: left;
		color: var(--error-500);
	}
}

.ant-form-item-has-error
	:not(.ant-input-disabled):not(.ant-input-borderless).ant-input {
	border-color: var(--error-300);
}
.ant-form-item-has-error
	.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input)
	.ant-select-selector {
	border-color: var(--error-300) !important;
}

.ant-form-item-has-error
	:not(.ant-input-affix-wrapper-disabled):not(
		.ant-input-affix-wrapper-borderless
	).ant-input-affix-wrapper {
	border-color: var(--error-300);
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
	font-weight: 400;
	background-color: var(--grey-100);
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
	background-color: var(--grey-50);
}

.ant-select-item-option-content {
	@apply text-sm text-grey-900;
}

.ant-select {
	&-arrow {
		@apply text-grey-500;
		& svg {
			width: 20px;
			height: 20px;
			flex-shrink: 0;
		}
	}
}

.mitra-input-field.ant-input {
	&-affix-wrapper {
		padding: 8px !important;
		&::before {
			height: 0;
		}
		.ant-input-prefix {
			height: 20px;
			width: 20px;
		}
		input {
			height: 20px;
			color: var(--grey-500);
		}
	}
}

.mitra-selector {
	@apply mt-1.5 h-[44px] rounded-md shadow-xs;
	.ant-select-selector {
		@apply flex !h-[44px] items-center !p-0 !px-3.5;
	}
	.ant-select-selection-item {
		font-size: 16px;
		color: var(--grey-500);
	}
	.ant-select-arrow {
		right: 16px;
	}
	.ant-select-clear {
		right: 14px !important;
	}
}
/* 
.ant-popover-inner {
	border-radius: 6px;
} */
.ant-popover-arrow {
	display: none;
}

.ant-popover-placement-bottom,
.ant-popover-placement-bottomLeft,
.ant-popover-placement-bottomRight,
.ant-popover-placement-top,
.ant-popover-placement-topLeft,
.ant-popover-placement-topRight {
	padding: 0px;
}

.ant-dropdown.formatter-menu {
	@apply rounded;
	box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
		0px 2px 4px -2px rgba(16, 24, 40, 0.06);

	.ant-dropdown-menu {
		&-root {
			@apply px-0 py-3;
		}
		&-submenu-title {
			@apply flex items-center justify-center pl-4 pr-3.5;
		}
	}
}

.add-screen .ant-modal-header {
	border-bottom: none !important;
}

.global-search {
	.ant-select-selector {
		height: 28px !important;
		border-radius: 8px !important;
		.ant-select-selection-placeholder {
			color: #5d6585 !important;
			font-weight: 400 !important;
			font-size: 14px;
			margin-left: 21px;
			margin-top: -2px;
		}
		.ant-select-selection-item {
			display: none;
		}
	}
	.ant-select-arrow {
		left: 10px;
		margin-top: -10px !important;
	}
	.ant-select-selection-search {
		margin-top: -2px !important;
		margin-left: 20px !important;
		input {
			color: #101828;
			font-weight: 400;
			font-size: 14px !important;
		}
	}
	.rc-virtual-list {
		padding-top: 1px;
		padding-bottom: 2px;
	}
	.rc-virtual-list-holder {
		/* max-height: 80vh !important;
	height: auto !important; */
	}
}

.global-search-dropdown {
	height: auto !important;
	border: 1px solid #eff1f7;
	box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
		0px 4px 6px -2px rgba(16, 24, 40, 0.03);
	border-radius: 8px;

	.ant-select-item-group:first-child {
		margin-top: 0px;
		/* margin-bottom: 10px; */
		/* padding-top: 16px; */
		border-top: unset !important;
	}

	.ant-select-item-group {
		border-top: 1px solid #e7e8f0;
		/* padding-top: 16px; */
		/* padding-top: 16px;
		padding-bottom: 16px; */
	}
	.ant-select-item {
		padding-top: 16px;
	}
}

.ant-select-dropdown {
	z-index: 99999;
}

.global-search-dropdown.ant-select-dropdown {
	z-index: 99991;
	width: 370px !important;

	.ant-select-item-option-grouped {
		margin-top: 2px;
		margin-bottom: 8px;
		padding-left: 10px;
		padding-right: 10px;
	}

	.ant-select-item.ant-select-item-option.ant-select-item-option-grouped {
		border-radius: 8px;
		margin: 0px 5px;
		padding: 0px;
	}
	.ant-select-item.ant-select-item-option.ant-select-item-option-grouped:last-child {
		/* margin-bottom: 22px; */
	}
	.ant-select-item-option-active {
		background: #f8f9fd;
		/* background-color: white;
	box-shadow: inset 0px 0px 0px 4px #f6f7fb;
	margin: 0px 5px;
	padding: 10px 10px; */
	}
	.ant-select-item.ant-select-item-option.ant-select-item-option-grouped.ant-select-item-option-active:hover {
		background-color: #f8f9fd;
		box-shadow: unset !important;
	}
}

.filtered.ant-select-item {
	min-height: 0px;
}

.input-preffixed .ant-select-selection-item {
	@apply ml-1 !cursor-default;
}

.table-selector-menu .ant-select-selector {
	padding: 0 28px !important;
}

.table-selector-menu .aggregate-select .ant-select-selector {
	padding-left: 12px !important;
}

.table-selector-menu
	.ant-select-disabled.ant-select:not(.ant-select-customize-input)
	.ant-select-selector {
	background: #eff1f7;
	border: 1.13458px solid #e7e8f0;
	@apply flex h-[40px] items-center;
}

.ant-popover-title {
	padding: 12px 16px 0 16px;
	border-bottom: 1px solid var(--grey-200);
}

.base-popover {
	.ant-popover-inner {
		background-color: var(--white);
		border: 1px solid var(--grey-200);
		box-shadow: var(--shadow-base);
		border-radius: 6px;
	}
	.ant-popover-inner-content {
		top: 0px;
		padding: 0px;
	}
}

.mitra-base-popover {
	.ant-popover-inner {
		box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014,
			0 9px 28px 8px #0000000d !important;
		border-radius: 8px;
	}
	.ant-popover-inner-content {
		top: 0px;
		padding: 0px;
	}
}

.sidebar-tabs {
	height: 47px;
	min-height: 47px;
	margin: 0 16px;
	.ant-tabs {
		&-content-holder {
			display: none;
		}
		&-nav {
			&-list {
				display: flex;
				justify-content: space-between;
				width: 100%;
				margin-right: 8px;
			}
			&-more {
				display: none;
			}
			margin-bottom: 0px;
		}
		&-tab {
			margin: 0px;
			padding: 12px 10px;
			&-active > div {
				font-weight: 500;
				color: var(--violet-700) !important;
			}
			&-btn {
				font-size: 14px;
				color: var(--grey-500);
			}
		}
	}
}

.import-file .ant-modal {
	&-title {
		color: #1b2139;
		font-weight: 500;
		font-size: 16px;
	}
	&-header {
		border-bottom: none;
	}
	&-body .import-upload {
		background: #ffffff;
		border: 1px solid #e7e8f0;
		border-radius: 8px;
	}
	&-footer {
		border-top: none;
	}
	&-close-x {
		svg {
			@apply h-[10px] w-[12px] fill-[var(--grey-400)];
		}
	}
	import-upload {
		background: red !important;
	}
}

.create-project-modal .ant-modal-body {
	display: flex;
}

.invite-member-modal {
	padding: 0px !important;
	.ant-modal-header {
		margin: 10px 0px 10px 0 !important;
		padding: 0px 10px;
		height: 40px;
	}
	.ant-modal-content {
		padding: 5px 0px !important;
	}
	.ant-modal-body {
		padding: 0px !important;
	}
}

.create-project-modal .ant-modal-content {
	padding: 0;
	.ant-input {
		&::placeholder {
			font-size: 16px;
			font-weight: 400;
			color: var(--grey-500) !important;
		}
		&:focus {
			border-color: var(--violet-400);
			box-shadow: theme(boxShadow.violet);
		}
		&:hover {
			border-color: var(--violet-400);
		}
	}
}

.new-button-detail .ant-popover-inner-content {
	padding: 0px;
	margin: 0px;
	border-radius: 8px !important;
	border: 1px solid var(--Gray-25, #f8f9fd);
	background: var(--Base-White, #fff);
	@apply rounded-md bg-white;

	/* Shadow/md */
	box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
		0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.edit-flap .ant-popover-inner-content {
	padding: 0px;
	margin: 0px;
	border-radius: 8px;
	border: 1px solid var(--grey-25);
	background: var(--base-white, #fff);

	/* Shadow/md */
	box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
		0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.new-button-tab {
	& .ant-tabs-nav {
		color: var(--grey-600);
	}
	& .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--violet-600);
	}

	& .ant-tabs-tab:hover {
		color: var(--violet-500);
	}

	& .ant-tabs-tab-btn:active {
		color: var(--violet-500);
	}
}

.ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar,
.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar {
	background: var(--violet-600);
}

.new-tab-button-detail {
	border-radius: 6px;
	background: var(--base-white, #fff);

	/* Shadow/lg */
	box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
		0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.button-user-detail .ant-popover-inner {
	display: inline-flex;
	/* height: 28px;
	width: 73px; */
	justify-content: center;
	align-items: center;
	gap: 8px;
	flex-shrink: 0;
	border-radius: 8px;
	border: 1px solid var(--gray-20300, #c7cbdd);
	background: var(--base-white, #fff);

	/* Shadow/sm */
	box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.06),
		0px 1px 3px 0px rgba(16, 24, 40, 0.1);

	& .ant-popover-inner-content {
		margin: 0px;
		padding: 0px;
	}
}

.tabble-attribute-menu .ant-popover-inner-content {
	padding: 0;
	border-radius: 8px;
	border: 1px solid var(--gray-2025, #f8f9fd);
	background: var(--base-white, #fff);
	width: 400px;

	/* Shadow/md */
	box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
		0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.add-attribute-menu {
	width: 400px;
	margin: 0px;
	padding: 0px;

	.ant-popover-inner-content {
		border-radius: 16px;
		padding: 0 !important;
	}
}

.change-connection-modal .ant-modal-content {
	padding: 24;
	border-radius: 16px;
	background: #fff;

	/* Shadow/md */
	box-shadow: 0px 2px 4px -2px rgba(16, 24, 40, 0.06),
		0px 4px 8px -2px rgba(16, 24, 40, 0.1);
	.ant-select-selector {
		display: flex;
		padding: 10px 14px;
		align-items: center;
		gap: 8px;
		align-self: stretch;
		border-radius: 8px;
		border: 1px solid var(--gray-20300, #c7cbdd);
		background: var(--base-white, #fff);

		/* Shadow/xs */
		box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
		height: 40px;
	}
	.ant-select-arrow {
		top: 48%;
		right: 14px;
	}
}

.connection-modal {
	.ant-modal {
		top: 20px !important;
		.ant-modal-content {
			height: 100% !important;
			border-radius: 16px;
			padding: 0 4px 20px 4px !important;
			.ant-modal-body {
				height: 100% !important;
			}
		}
	}
}

.test-api-modal {
	.ant-modal {
		.ant-modal-content {
			border-radius: 16px;
			padding: 0px !important;
		}
		max-width: 1400px;
		max-height: 720px;
		width: auto !important;
		height: auto !important;
	}
}

.connection-template-modal .ant-modal-content {
	padding: 24;
	border-radius: 6px;
	border: 1px solid var(--gray-20200, #e7e8f0);
	background: #fff;

	/* Shadow/lg */
	box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03),
		0px 12px 16px -4px rgba(16, 24, 40, 0.08);
}

.change-connection-panel .ant-modal-content {
	padding: 32px;
	.ant-select-selector {
		display: flex;
		padding: 10px 14px;
		align-items: center;
		gap: 8px;
		align-self: stretch;
		border-radius: 8px;
		border: 1px solid var(--gray-20300, #c7cbdd);
		background: var(--base-white, #fff);

		/* Shadow/xs */
		box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
		height: 35px;
	}
	.ant-select-arrow {
		top: 48%;
		right: 16px;
	}
}

.add-legacy-cube-ft {
	&.ant-input-affix-wrapper > input.ant-input {
		font-weight: 500;
		font-size: 14px;
		color: var(--grey-900);
	}
}

.add-legacy-cube-ft-preview {
	&.ant-input-affix-wrapper > input.ant-input[disabled] {
		font-weight: 500;
		font-size: 14px;
		color: var(--grey-300) !important;
		&::placeholder {
			color: var(--grey-300) !important;
		}
	}
}

.base-tooltip {
	z-index: 99999;
	&.large-tooltip {
		max-width: none !important;

		.ant-tooltip-inner {
			display: block;
			pre {
				max-width: 400px !important;
				max-height: 300px;
				overflow: auto;
			}
		}
	}
	&.dark-tooltip {
		& .ant-tooltip-inner {
			background: var(--white) !important;
			.base-tooltip-text {
				color: var(--grey-500);
			}
			.base-tooltip-header {
				font-weight: 500 !important;
				color: var(--grey-700) !important;
				font-weight: 400;
				font-size: 0.75rem /* 12px */;
				line-height: 1.1rem /* 16px */;
			}
		}
	}

	&.medium-tooltip-a {
		& .ant-tooltip-inner {
			width: 270px !important;
		}
	}

	&.medium-tooltip-b {
		& .ant-tooltip-inner {
			width: 330px !important;
		}
	}

	& .ant-tooltip-inner {
		background: var(--grey-900);
		display: flex;
		border-radius: 8px;
		padding: 8px 12px;
		flex-direction: column;
		align-self: stretch;
		align-items: center;
		justify-content: center;
	}
	&__no-padding {
		padding: 0 !important;
	}
	& .ant-tooltip-arrow {
		display: none !important;
	}
}
.base-tooltip-text {
	color: white;
	font-weight: 400;
	font-size: 0.75rem /* 12px */;
	line-height: 1.1rem /* 16px */;
}

.base-tooltip-text-type-2 {
	color: white;
	font-weight: 500;
	font-size: 0.75rem /* 12px */;
	line-height: 18px;
	text-align: center;
}

.base-options-right-tooltip {
	& .ant-tooltip-inner {
		display: flex;
		padding: 8px 12px;
		flex-direction: column;
		align-items: flex-start;
		flex: 1 0 0;
		align-self: stretch;
		border-radius: 8px;
		width: 306px;
		margin-left: 80px;
	}
	& .ant-tooltip-arrow {
		margin-left: 80px;
	}
}

.skw-app {
	.ant-pagination .ant-pagination-jump-prev {
		color: var(--sankhya-green-600) !important;
	}
	.ant-pagination-item.ant-pagination-item {
		&:hover {
			> a {
				color: var(--sankhya-green-600) !important;
			}
		}
		&.ant-pagination-item-active {
			border-color: var(--sankhya-green-600) !important;
			> a {
				color: var(--sankhya-green-600) !important;
			}
		}
	}

	.button-wrapper__type-text:hover {
		box-shadow: none !important;
	}

	.mitra-popover-wrapper.no-skw-admin {
		#option-0,
		#option-2 {
			display: none;
		}
	}

	.mitra-toggle {
		.edit-chip {
			border: 1px solid var(--grey-300);
			box-shadow: 0px 0px 0px 3px var(--grey-10),
				0px 1px 2px 0px rgba(16, 24, 40, 0.1);
		}
	}

	.ai-input-box {
		&.focused {
			border: 1px solid var(--sankhya-green-700) !important;
			box-shadow: 0px 0px 0px 3px var(--grey-10),
				0px 1px 2px 0px rgba(16, 24, 40, 0.1) !important;
		}
	}

	.mitra-radio-group .button {
		border-color: var(--grey-300) !important;
		background: white !important;
	}
	.mitra-radio-group .radio-group-active.button {
		background: var(--grey-50) !important;
		box-shadow: 1px 5px 15px -4px #875bf742 !important;
	}
	.mitra-radio-group .example-prompt {
		border: 1px solid var(--grey-300) !important;
	}
	.mitra-radio-group .radio-group-active.button .example-prompt {
		border: 1px solid var(--grey-300) !important;
	}

	#create-new-btn.database-create-new-btn {
		> svg {
			color: var(--sankhya-green-700) !important;
		}
	}

	.no-access-wrapper {
		height: 100% !important;
		> div {
			@media (max-width: 1366px) {
				width: 1234px;
			}
		}
		.left-content {
			max-width: 627px;
			@media (max-width: 1366px) {
				width: 1234px;
			}
		}
	}

	.lds-ring {
		div {
			border-color: var(--sankhya-green-600) transparent transparent transparent;
		}
	}

	.breadcrumb-wrapper {
		&:hover {
			background-color: var(--grey-50);
		}
	}

	.preppend-content {
		span,
		svg {
			color: var(--grey-500) !important;
		}
	}
	/* 
	.text-violet-600 {
		color: var(--sankhya-green-600) !important;
	} */

	svg.animate-spin:not(.filled-parent) {
		color: var(--sankhya-green-600) !important;
	}

	.connection-list-menu {
		.button-wrapper.outlined svg {
			color: var(--sankhya-green-600) !important;
		}
	}

	.base-uploader {
		.upload-override-button {
			border-color: var(--sankhya-green-600);
			background-color: var(--sankhya-green-50);
			color: var(--sankhya-green-600);
		}

		.select-text {
			color: var(--primary-700);
			font-weight: 500;
		}
		.item-subtext {
			color: var(--primary-600);
		}
		> span {
			border-color: var(--primary-300);
			background-color: var(--primary-25);
		}
	}

	.rmv-text {
		color: var(--violet-500);
	}

	.content-badge--bia {
		position: static !important;
		background-color: var(--grey-100);
		width: 32px;
		padding: 0 6px;
		font-size: 12px;
		margin-left: 8px;
		height: 20px;
	}

	.input {
		border-color: var(--sankhya-green-600) !important;
	}

	.table-add-menu {
		.add-icon {
			color: var(--grey-500) !important;
		}
	}

	.tab-list {
		.active,
		.tab:not(.menu):hover {
			color: var(--grey-900) !important;
			background: var(--grey-50);
		}

		.table-area {
			background: var(--grey-50);
		}
		.shadow-right,
		.shadow-left {
			svg {
				color: var(--grey-400) !important;
			}
		}

		.active svg,
		.tab:hover svg {
			color: var(--grey-900);
		}
	}

	.append-item-tile {
		span,
		svg {
			color: var(--sankhya-green-600);
			font-weight: 400 !important;
		}
	}

	.connector-edit-btn {
		svg {
			color: var(--grey-500) !important;
		}
	}

	.table-wrapper,
	.knowmore-modal {
		.button-wrapper.outlined {
			svg {
				color: var(--sankhya-green-600) !important;
			}
		}

		.ant-badge-count,
		.item-chip {
			background-color: var(--sankhya-green-50) !important;
			color: var(--sankhya-green-600) !important;
		}
	}

	.ant-select .ant-select-arrow svg:not(.simple-arrow) {
		color: var(--grey-400) !important;
	}

	.access-select {
		.ant-select-item-empty:has(svg) {
			display: flex;
			justify-content: center;
		}
	}

	img[src*='violet'],
	.primary-filter {
		filter: hue-rotate(214deg) saturate(0.419) brightness(1.9);
	}

	.sankhya-store-modal {
		.ant-modal {
			max-width: 1422px !important;

			/* @media (max-width: 1920px) {
				width: 90vw !important;
				max-width: unset !important;
			} */

			@media (max-width: 1569px) {
				width: 96vw !important;
				max-width: unset !important;
			}
			/* @media (min-width: 1580px) {
				width: 1422px !important;
			} */
		}
	}

	.tab-list {
		.tab.active {
			svg,
			.span,
			span {
				color: var(--sankhya-green-700) !important;
			}
			.add-icon {
				color: var(--grey-500) !important;
			}
		}
	}

	.ant-popover-inner-content {
		.project-item {
			height: 38px !important;
			margin: 10px 0 !important;
		}
	}

	.div-bg-color {
		background-color: var(--sankhya-green-50) !important;
	}
}

.skw-app
	.button-wrapper.outlined:not(.disabled):not(.thinking-btn)
	span.button-text {
	color: var(--grey-900) !important;
}

.skw-app .top-card-icon {
	background-color: var(--sankhya-green-50) !important;
}

.skw-app .top-card-icon svg {
	color: var(--sankhya-green-600) !important;
}

.skw-app
	.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
	background-color: var(--sankhya-green-50) !important;
}

.skw-app .toogle-content,
.skw-app .button-use:hover,
.skw-app .add-button:hover,
.skw-app .ant-btn:not(.ant-btn-circle):not(.cancel-button),
.skw-app .table-att-menu .ant-btn,
.skw-app .table-att-menu .ant-btn,
.table-att-menu.skw-app .ant-btn {
	background-color: var(--sankhya-green-600) !important;
	background: var(--sankhya-green-600) !important;
}

.skw-app .ant-btn.cancel-button:hover {
	border-color: var(--sankhya-green-600) !important;
}

.skw-app .ant-btn.cancel-button {
	color: var(--grey-900) !important;
}

.skw-app .change-connection-modal .ant-modal-content {
	svg {
		color: var(--sankhya-green-600) !important;
	}
}

.skw-app .logo-background,
.skw-app .active.button,
.skw-app
	.button-wrapper.disabled:not(.outlined):not(.button-wrapper__type-text),
.skw-app .ant-btn[disabled],
.skw-app .table-att-menu .ant-btn[disabled],
.table-att-menu.skw-app .ant-btn[disabled] {
	background-color: var(--sankhya-green-50) !important;
	background: var(--sankhya-green-50) !important;
}

.skw-app .button-wrapper.disabled.outlined,
.button-wrapper.disabled.button-wrapper__type-text {
	opacity: 0.5 !important;
	.button-text {
		color: var(--grey-300) !important;
	}
	svg {
		color: var(--grey-300) !important;
	}
}

.skw-app .ant-switch-checked:not(.ant-switch-disabled) {
	background-color: var(--sankhya-green-600) !important;
}

.skw-app .add-button:hover,
.skw-app .ant-select-selector:hover,
.skw-app .ant-input-affix-wrapper:hover,
.skw-app .ant-input:hover {
	border-color: var(--sankhya-green-600) !important;
}

.skw-app .delete:hover,
.skw-app .cancel:hover,
.skw-app .simple:hover,
.skw-app .button-use:hover,
.skw-app
	.ant-select-focused:not(.ant-select-disabled).ant-select:not(
		.ant-select-customize-input
	)
	.ant-select-selector,
.skw-app .ant-input-affix-wrapper-focused:not(.input-search),
.skw-app .list-card-chip:hover,
.skw-app .ant-input:focus {
	border-color: var(--sankhya-green-600) !important;
	box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05),
		0px 0px 0px 4px var(--sankhya-green-50) !important;
}

.skw-app .delete:hover {
	color: white !important;
}

.skw-app {
	.ant-switch {
		&-checked:not(.highlighted) {
			background-color: var(--grey-200) !important;
		}
	}

	.ant-switch {
		&-checked.highlighted {
			background-color: var(--grey-600) !important;
		}
	}

	.table-area {
		background: var(--grey-50) !important;
	}

	.switcher-container {
		border: 1px solid rgba(102, 204, 102, 0.3) !important;

		box-shadow: 0px 0px 22.9px 0px rgba(102, 204, 102, 0.2) !important;

		&-analyst {
			border: 1px solid var(--primary-300);
			box-shadow: 0px 0px 22.9px 0px rgba(21, 112, 239, 0.2);
		}

		&-button {
			&:hover {
				background: var(--sankhya-green-50) !important;
			}
			&.selected {
				background: rgba(102, 204, 102, 0.1) !important;
				span {
					color: #50b250 !important;
				}
				svg {
					color: #50b250 !important;
				}
			}
		}
	}

	.outlined.outlined-active {
		border: 1px solid var(--sankhya-green-600) !important;
		background-color: #f0fff0 !important;
		svg {
			color: var(--sankhya-green-600) !important;
		}
	}

	.wrapper-copilot {
		height: calc(100vh - 170px) !important;
	}

	.as-analyst {
		.selected-thinking.skw-btn {
			background-color: #f0fff0 !important;
			border: 1px solid var(--sankhya-green-600) !important;
			span {
				color: var(--sankhya-green-600) !important;
			}
			svg {
				color: var(--sankhya-green-600) !important;
			}
		}

		.outlined.outlined-active {
			border: 1px solid var(--sankhya-green-600) !important;
			background-color: #f0fff0 !important;
			svg {
				color: var(--sankhya-green-600) !important;
			}
		}
	}

	.link-hint {
		@apply !text-grey-600;
		&:hover {
			text-decoration: underline !important;
			color: var(--grey-900);
		}
	}

	.content {
		&-icon {
			&__square {
				filter: none !important;
				opacity: 1 !important;
			}
		}
	}

	.simple-square .content-icon {
		color: white !important;
	}

	.button-wrapper.outlined:not(.button-wrapper__type-text) {
		background-color: var(--white) !important;
		&:hover {
			box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05), 0px 0px 0px 4px var(--grey-25) !important;
		}
		&:active {
			background-color: var(--grey-50) !important;
		}
	}
}

.skw-app .toogle,
.skw-app .active.button {
	border-color: var(--sankhya-green-600) !important;
}

.skw-app .clear-button {
	background-color: var(--error-600) !important;
}

.skw-app {
	.sankhya-simple-logo {
		width: 16px;
		height: 16px;
		margin-left: 2px;
		margin-right: 5px;
	}

	.ant-checkbox {
		&-inner {
			border-color: var(--sankhya-green-600) !important;
			background-color: var(--ankhya-green-25) !important;
		}
	}

	.ant-checkbox-checked {
		.ant-checkbox & .ant-checkbox-inner {
			&::after {
				border: 2px solid var(--sankhya-green-600);
			}
		}
		&::after {
			border: 1px solid var(--sankhya-green-600) !important;
		}
	}

	.ant-checkbox-checked .ant-checkbox-inner::after {
		border-color: var(--sankhya-green-600) !important;
	}

	.ant-input-affix-wrapper.input-search,
	.ant-input-affix-wrapper-focused.input-search {
		/* border: none !important; */
		border: 1px solid var(--grey-300);

		box-shadow: none !important;
		&:focused {
			border: 1px solid var(--grey-300) !important;
			border-color: var(--grey-300) !important;
		}
		&:hover {
			border: 1px solid var(--grey-300) !important;
			border-color: var(--grey-300) !important;
		}
	}
}

.skw-app .cancel-button {
	background-color: white !important;
}

.skw-app .cancel-button .button-text {
	color: black !important;
}

empty_profiles .skw-app .folder-element span,
.skw-app .folder-element svg {
	color: var(--sankhya-green-600) !important;
}

.skw-app.new-button-tab {
	& .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
		color: var(--sankhya-green-600) !important;
	}

	& .ant-tabs-tab:hover {
		color: var(--sankhya-green-600) !important;
	}

	& .ant-tabs-tab-btn:active {
		color: var(--sankhya-green-600) !important;
	}
}

.skw-app .mitra-link {
	svg {
		color: var(--sankhya-green-600) !important;
	}
	span {
		color: var(--sankhya-green-600) !important;
	}
}

.skw-app .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar,
.skw-app .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar {
	background: var(--sankhya-green-600) !important;
}

.solutions-tooltip .ant-tooltip-inner {
	padding: 0px;
	border-radius: 8px;
	overflow: hidden;
	background: white;
}

.sankhya-store-modal .ant-modal-content {
	padding: 0px !important;
	border-radius: 16px;
	overflow: hidden;
}

.skw-app .background-custom {
	background: radial-gradient(at left bottom, #b8edb8b6, transparent 25%),
		radial-gradient(at top right, #d3d6db, transparent 0%);
}

.delete-button.simple:hover {
	background-color: #d92d20 !important;
	box-shadow: 0px 1px 2px #fee4e2, 0px 0px 0px 4px #fee4e2 !important;
}

.cancel-button-outlined.ant-btn:hover {
	border-color: #d9d9d9;
	color: var(--violet-600);
	box-shadow: 0px 1px 2px rgba(179, 179, 179, 0.05),
		0px 0px 0px 4px var(--grey-100);
}

.base-tooltip.query .ant-tooltip-inner {
	width: fit-content;
}

.my-sql-tag {
	font-family: Inter;
	font-size: 12px;
	font-style: normal;
	font-weight: 500;
	line-height: 18px;
	color: var(--primary-500);
	background-color: var(--primary-50);
	border-radius: 14px;
	padding: 0px 9px;
	height: fit-content;
	margin: auto auto auto 0;
}

.ant-select-item:has(.jdbc-content) {
	padding: 0 !important;
}
