<template>
	<div class="!top-0 !h-full !w-full absolute py-[64px] bg-white">
		<div
			class="flex flex-col items-center"
			:class="{ 'skw-app': useWhiteLabel().isSankhyaClient }"
		>
			<div
				class="mb-[20px] flex items-center justify-center rounded-full"
				:style="{
					border: `12px solid var(--grey-50)`,
					background: `var(--grey-200)`,
					width: `79px`,
					height: `79px`
				}"
			>
				<HugeiconsIcon
					:icon="SearchRemoveIcon"
					:size="30"
					color="var(--grey-600)"
				/>
			</div>
			<span class="text-center text-grey-900 font-medium text-xl">
				{{
					workspaceStore.previewProjectDesktopWithoutScreen
						? $t('PROJECT.no_web_screens_created')
						: $t('PROJECT.no_mobile_screens_created')
				}}
			</span>
			<div
				class="text-center text-grey-600 text-base mt-2 font-normal"
				:class="{
					'max-w-[545px]': workspaceStore.previewProjectDesktopWithoutScreen,
					'max-w-[345px]': workspaceStore.previewProjectMobileWithoutScreen
				}"
			>
				{{
					workspaceStore.previewProjectDesktopWithoutScreen
						? $t('PROJECT.no_web_screens_created_description')
						: $t('PROJECT.no_mobile_screens_created_description')
				}}
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
	import { SearchRemoveIcon } from '@hugeicons-pro/core-stroke-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';

	const workspaceStore = useWorkspaceStore();
</script>
<style lang="postcss" scoped></style>
