<template>
	<div>
		<!-- ICONS -->
		<div class="mb-4">
			<!-- Icon Search -->
			<div class="flex flex-col">
				<div class="mb-3">
					<a-input
						v-model:value="iconSearchQuery"
						:placeholder="'Buscar ícones...'"
						class="h-[38px]"
						allow-clear
					>
						<template #prefix>
							<svgo-lab-search
								color="var(--grey-500)"
								class="w-[20px] h-[20px]"
							/>
						</template>
					</a-input>
				</div>
			</div>
			<div
				ref="iconsContainer"
				class="icons-container h-40 overflow-y-auto"
				@scroll="handleIconScroll"
			>
				<!-- Virtual scroll wrapper -->
				<div
					class="virtual-scroll-wrapper"
					:style="{ height: totalHeight + 'px', position: 'relative' }"
				>
					<!-- Initials option (always visible when not searching) -->
					<div
						v-if="!iconSearchQuery.trim() && startIndex === 0"
						class="icon-wrapper absolute"
						:style="{
							backgroundColor:
								selectedIcon === GLOBALS.INITIALS
									? selectedColor
									: 'unset',
							top: '12px',
							left: '12px'
						}"
						@click="handleSetIcon(GLOBALS.INITIALS)"
					>
						<span
							class="text-[8px] font-bold uppercase"
							:style="{
								color:
									selectedIcon === GLOBALS.INITIALS
										? 'var(--white)'
										: undefined
							}"
							>{{
								useStringFormat().getInitals(projectName).slice(0, 2).replace('(', '')
							}}</span
						>
					</div>

					<!-- Virtual scrolled icons -->
					<div
						v-for="(iconName, idx) in visibleIcons"
						:key="`icon-${startIndex + idx}`"
						class="icon-wrapper absolute"
						:style="{
							backgroundColor:
								selectedIcon === iconName
									? selectedColor
									: 'unset',
							top:
								Math.floor(
									(startIndex + idx + (!iconSearchQuery.trim() ? 1 : 0)) / itemsPerRow
								) *
									itemHeight +
								12 +
								'px',
							left:
								((startIndex + idx + (!iconSearchQuery.trim() ? 1 : 0)) % itemsPerRow) * 40 +
								12 +
								'px'
						}"
						@click="handleSetIcon(iconName)"
					>
						<svg-icon
							size="16"
							:style="{
								color:
									selectedIcon === iconName ? 'var(--white)' : undefined
							}"
							class="icon-wrapper__svg"
							type="mdi"
							:path="(iconsMap as any)[iconName]"
						></svg-icon>
					</div>

					<!-- No results message -->
					<div
						v-if="iconSearchQuery.trim() && filteredIcons.length === 0"
						class="absolute flex items-center justify-center text-gray-500 text-sm text-center opacity-80"
						:style="{
							top: '0',
							left: '0',
							right: '0',
							height: containerHeight - 20 + 'px'
						}"
					>
						{{ $t('GLOBAL.no_icon_found') }} "{{ iconSearchQuery }}"
					</div>
				</div>
			</div>
			<div class="flex justify-between items-center mt-2">
				<span class="font-medium text-grey-400 text-sm"></span>
				<span class="text-xs text-gray-500 opacity-80">
					{{ filteredIcons.length }}
				</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { GLOBALS } from '~/helpers/contants/global_constants';

	defineProps({
		selectedIcon: {
			type: String,
			required: true
		},
		selectedColor: {
			type: String,
			required: true
		},
		projectName: {
			type: String,
			required: true
		}
	});

	const emit = defineEmits(['setIcon']);

	const { iconsMap, searchIcons } = useMappedIcons();

	// Icon search functionality
	const iconSearchQuery = ref('');
	const filteredIcons = computed(() => {
		if (!iconSearchQuery.value.trim()) {
			return Object.keys(iconsMap);
		}
		return searchIcons(iconSearchQuery.value);
	});

	// Virtual scroll configuration
	const iconsContainer = ref<HTMLElement>();
	const itemHeight = 40; // 32px icon + 8px gap
	const itemsPerRow = ref(8); // Will be calculated dynamically
	const containerHeight = 160; // h-40 = 160px

	// Virtual scroll state
	const scrollTop = ref(0);
	const startIndex = computed(() => {
		const startRow = Math.floor(scrollTop.value / itemHeight);
		return Math.max(0, startRow * itemsPerRow.value);
	});
	const endIndex = computed(() => {
		const endRow = Math.ceil((scrollTop.value + containerHeight) / itemHeight);
		return Math.min(filteredIcons.value.length, endRow * itemsPerRow.value);
	});

	// Visible icons for virtual scroll
	const visibleIcons = computed(() => {
		return filteredIcons.value.slice(startIndex.value, endIndex.value);
	});

	// Total height for virtual scroll
	const totalHeight = computed(() => {
		const totalRows = Math.ceil(filteredIcons.value.length / itemsPerRow.value);
		return totalRows * itemHeight;
	});

	// Handle scroll event
	const handleIconScroll = (event: Event) => {
		const target = event.target as HTMLElement;
		scrollTop.value = target.scrollTop;
	};

	// Calculate items per row based on container width
	const calculateItemsPerRow = () => {
		if (iconsContainer.value) {
			const containerWidth = iconsContainer.value.clientWidth - 24; // minus padding
			itemsPerRow.value = Math.floor(containerWidth / 40); // 32px + 8px gap
		}
	};

	// Watch for container resize
	onMounted(() => {
		calculateItemsPerRow();
		window.addEventListener('resize', calculateItemsPerRow);
	});

	onUnmounted(() => {
		window.removeEventListener('resize', calculateItemsPerRow);
	});

	function handleSetIcon(icon: string) {
		emit('setIcon', icon);
	}
</script>

<style lang="postcss" scoped>
	.icons-container {
		position: relative;
		padding: 12px;
		background-color: var(--grey-50);
		border-radius: 8px;
	}

	.virtual-scroll-wrapper {
		position: relative;
		width: 100%;
	}

	.icon-wrapper {
		width: 32px;
		height: 32px;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.2s ease;
		border: 1px solid var(--grey-200);
		background-color: var(--white);
		&:hover {
			background-color: var(--grey-100);
		}
	}

	.icon-wrapper__svg {
		color: var(--grey-700);
	}
</style>