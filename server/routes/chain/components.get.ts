import axios from 'axios';

export default defineEventHandler(async (event) => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const { _dummy } = getQuery(event);
	const { MITRA_CHAIN_TOKEN } = useRuntimeConfig();

	const axiosInstance = axios.create({
		baseURL: 'https://prod2.mitrasheet.com:8080/rest/v0',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${MITRA_CHAIN_TOKEN}`
		}
	});

	try {
		const { data } = await axiosInstance.get(`/Custom%20Components`);
		
		const mapped = data.content?.reduce(
					(acc: AIChainComponentRefs[], item: AIChainComponent) => {
						acc.push({
							name_ptbr: item['Nome Store'],
							name_en: item['Nome Store (EN)'],
							id: item.ID,
							key: item['Descrição']
						});
						return acc;
					},
					[]
				);

		return mapped;
	} catch (error) {
		// eslint-disable-next-line no-console
		console.error('Failed to fetch AI Chain Components:', error);

		throw createError({
			statusCode: 500,
			statusMessage: 'Failed to fetch AI Chain Components'
		});
	}
});
