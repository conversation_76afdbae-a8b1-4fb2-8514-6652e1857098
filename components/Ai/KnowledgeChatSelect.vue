<template>
	<div class="know-wrapper">
		<HugeiconsIcon
			:icon="AiBrain04Icon"
			size="20"
			color="var(--grey-900)"
			class="shrink-0"
		></HugeiconsIcon>

		<div
			v-if="localSelectedTags.length"
			class="flex gap-2"
		>
			<a-tag
				v-for="tag in visibleTags"
				:key="tag.name"
				:color="tag.color"
				:value="tag.name"
				class="know-tag"
			>
				{{ tag.name }}
			</a-tag>

			<template v-if="remainingCount > 0">
				<span
					class="tags-text"
					@click.stop.prevent="handleTagListDialog"
				>
					+ {{ remainingCount }} itens
				</span>
			</template>
		</div>
		<div
			v-else
			class="select-text"
			@click.stop.prevent="handleTagListDialog"
		>
			{{ $t('GLOBAL.select') }}
		</div>

		<a-popover
			v-model:visible="tagListController"
			overlay-class-name="w-[200px] mitra-base-popover"
			trigger="click"
			placement="bottom"
			width="300px"
		>
			<div
				class="flex items-center justify-center cursor-pointer"
				@click.stop.prevent="handleTagListDialog"
			>
				<svg-icon
					type="mdi"
					:path="mdiChevronDown"
					color="var(--grey-500)"
					class="content-icon"
					size="20"
				></svg-icon>
			</div>

			<template #content>
				<div v-if="tags.length > 1" class="cursor-pointer check-all">
					<a-checkbox
						color="var(--grey-900)"
						@change="() => toggleTag(null)"
					/>
					<span class="check-all-text"> 
						{{ $t('GLOBAL.check_all') }}
					</span>
				</div>

				<div
					v-for="tag in tags"
					:key="tag.name"
					class="cursor-pointer tags-list"
					:style="
						localSelectedTags.some((t) => t.name === tag.name)
							? 'background: var(--Gray-50, #f6f7fb)'
							: ''
					"
					@click.stop.prevent="toggleTag(tag)"
				>
					<a-checkbox
						:checked="localSelectedTags.some((t) => t.name === tag.name)"
						color="var(--grey-900)"
					/>
					<a-tag
						:color="tag.color"
						:value="tag.name"
						class="know-tag"
						style="border: none; font-size: 12px"
					>
						{{ tag.name }}
					</a-tag>
				</div>
			</template>
		</a-popover>
	</div>
</template>

<script setup lang="ts">
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { AiBrain04Icon } from '@hugeicons-pro/core-stroke-rounded';
	import { mdiChevronDown } from '@mdi/js';

	const tagListController = ref(false);
	const visibleTags = computed(() => localSelectedTags.value.slice(0, 2));
	const remainingCount = computed(() =>
		Math.max(localSelectedTags.value.length - 2, 0)
	);

	const tags = ref<
		{
			id: number | null;
			name: string;
			color: string;
			createdAt: Date | null;
			createdBy: string | null;
			updatedAt: Date | null;
			updatedBy: string | null;
		}[]
	>([]);

	const localSelectedTags = ref<
		{
			id: number | null;
			name: string;
			color: string;
			createdAt: Date | null;
			createdBy: string | null;
			updatedAt: Date | null;
			updatedBy: string | null;
		}[]
	>([]);

	const emit = defineEmits(['update-tags']);

	watch(
		() => localSelectedTags.value, (tags: any[]) => {
			emit("update-tags", tags)
		},
		{ deep: true }
	);

	const toggleTag = (tag: any) => {
		if (!tag) {
			if (localSelectedTags.value.length === tags.value.length) {
				localSelectedTags.value = [];
			} else {
				localSelectedTags.value = [...tags.value];
			}
			return;
		}
		const exists = localSelectedTags.value.some(
			(t: { name: any }) => t.name === tag.name
		);

		if (exists) {
			localSelectedTags.value = localSelectedTags.value.filter(
				(t: {
					id: number | null;
					name: string;
					color: string;
					createdAt: Date | null;
					createdBy: string | null;
					updatedAt: Date | null;
					updatedBy: string | null;
				}) => t.name !== tag.name
			);
		} else {
			localSelectedTags.value.push(tag);
		}
	};

	function handleTagListDialog() {
		tagListController.value = true;
	}

	loadTagList();

	async function loadTagList() {
		const workspaceStore = useWorkspaceStore();
		const workspaceId = workspaceStore.selectedWorkspace.id;
		const { data } = await useLegacyService().getTagsByWorkspaceId(workspaceId);
		tags.value = data.tags;
	}
</script>

<style scoped lang="postcss">
	.know {
		&-tag {
			border-radius: 100px;
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0;
		}
		&-wrapper {
			padding: 0 8px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 8px;

			border-radius: 100px;
			border: 1px solid var(--Primary-300, #c3b4fd);
			background: var(--Base-White, #fff);
			box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px #f4ebff;
		}
	}
	.tags-text {
		color: var(--Gray-700, #303854);
		font-family: Inter;
		font-size: 13px;
		font-style: normal;
		font-weight: 500;
		line-height: 20px;
	}
	.check-all {
		display: flex;
		padding: 8px 14px;
		align-items: center;
		gap: 8px;
		align-self: stretch;
		border-bottom: 1px solid var(--Gray-200, #e7e8f0);

		&-text {
			color: var(--Gray-900, #0e1428);
			font-family: Inter;
			font-size: 13px;
			font-style: normal;
			font-weight: 400;
			line-height: 20px;
			padding: 0 4px;
		}
	}
	.tags-list {
		display: flex;
		padding: 8px 14px;
		align-items: center;
		gap: 8px;
		align-self: stretch;
	}
	.select-text {
		color: var(--Gray-500, #5d6585);
		font-family: Inter;
		font-size: 13px;
		font-style: normal;
		font-weight: 500;
		line-height: 20px;
	}
	:deep(.ant-select-item-option) {
		gap: 8px !important;
	}
	:deep(.ant-select-selector) {
		height: auto !important;
	}
	:deep(.ant-checkbox-checked .ant-checkbox-inner::after) {
		border-color: var(--violet-600);
	}
	:deep(.ant-popover-inner-content) {
		padding: 0 !important;
		widows: 300px !important;
	}
</style>
