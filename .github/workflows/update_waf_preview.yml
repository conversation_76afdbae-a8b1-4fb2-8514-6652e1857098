name: Update WAF Preview Branch (Reusable)

on:
  workflow_call:

jobs:
  update-branch:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout preview-waf branch
        uses: actions/checkout@v3
        with:
          ref: 'preview-waf'

      - name: Update branch from preview
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git fetch origin preview
          git reset --hard origin/preview
          git push origin preview-waf --force