<template>
	<div class="workspace-ai-tapume">
		<svg-icon
			class="animate-spin mb-4"
			size="50"
			type="mdi"
			color="var(--alt-grey-800, var(--violet-600))"
			:path="mdiLoading"
		></svg-icon>
		<div class="font-semibold text-2xl text-grey-700 mb-1">
			{{ $t('AI.creating_project') }}
		</div>
		<div class="text-xs text-grey-400">{{ $t('AI.few_minutes') }}</div>
		<div class="text-sm text-grey-700 mt-4">{{ currentState }}</div>
	</div>
</template>
<script lang="ts" setup>
	import { mdiLoading } from '@mdi/js';
	import { i18n } from '~~/plugins/i18n';

	const workspaceStore = useWorkspaceStore();
	const { selectedProject } = storeToRefs(workspaceStore);
	const created = ref(false);

	const currentState = computed(() => {
		return (
			(selectedProject.value.id
				? useCopilotStore().waiting
					? i18n.global.t('AI.executing_plan')
					: i18n.global.t('AI.planning')
				: i18n.global.t('AI.creating_project')) + '...'
		);
	});

	setTimeout(() => {
		created.value = true;
	}, 2000);
</script>
<style scoped lang="postcss">
	.workspace-ai-tapume {
		z-index: 10;
		position: absolute;
		width: calc(100% - 400px) !important;
		height: 100%;
		background-color: white;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
</style>
