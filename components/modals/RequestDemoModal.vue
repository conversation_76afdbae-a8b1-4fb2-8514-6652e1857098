<template>
	<a-modal
		:visible="visible"
		destroy-on-close
		width="560px"
		height="676px"
		centered
		:closable="false"
		wrap-class-name="base-mitra-modal__flat"
		@close="closeModal"
	>
		<template #title>
			<base-content-header
				title="Estou interessado"
				title-class="text-grey-900"
				@cancel="closeModal"
			></base-content-header>
		</template>

		<div class="p-6">
			<h6 class="text-grey-500 text-sm leading-5">
				Preencha o formulário abaixo para que um de nossos executivos entre em contato.
			</h6>
			<a-form
				:model="requestForm"
				class="mt-4"
				name="request-demo-form"
				autocomplete="off"
				layout="vertical"
			>
				<a-form-item
					name="name"
					:label="$t('GLOBAL.name')"
					v-bind="validateInfos.name"
				>
					<a-input
						v-model:value="requestForm.contactName"
						:placeholder="$t('GLOBAL.insert')"
						class="h-[40px]"
					>
					</a-input>
				</a-form-item>

				<a-form-item
					name="email"
					:label="$t('GLOBAL.email')"
					v-bind="validateInfos.email"
				>
					<a-input
						v-model:value="requestForm.email"
						class="rounded-md h-[40px]"
						:placeholder="$t('GLOBAL.email')"
					>
					</a-input>
				</a-form-item>

				<a-form-item
					name="phone"
					:label="$t('GLOBAL.phone')"
					v-bind="validateInfos.phone"
				>
					<a-input
						v-model:value="requestForm.phone"
						v-mask="['(##) ####-####', '(##) #####-####']"
						class="rounded-md h-[40px]"
						:placeholder="$t('GLOBAL.phone')"
					>
					</a-input>
				</a-form-item>
			</a-form>
		</div>

		<template #footer>
			<BaseContentFooter
				container-class="!pt-0 base-mitra-footer"
				:divider="false"
				:confirm-text="$t('GLOBAL.actions.send')"
				:loading="loading"
				:cancel-props="{
					outlined: true,
					typeText: false
				}"
				@confirm="onSubmit"
				@cancel="closeModal"
			/>
		</template>
	</a-modal>
</template>

<script lang="ts" setup>
	import { Form } from 'ant-design-vue';

	const props = defineProps({
		visible: {
			type: Boolean,
			default: false
		},
		loading: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['submit', 'update:visible']);

	interface RequestForm {
		contactName: string;
		email: string;
		phone: string;
	}

	const useForm = Form.useForm;

	const requestForm = reactive<RequestForm>({
		contactName: '',
		email: '',
		phone: ''
	});

	const rulesRef = reactive({
		name: [
			{
				required: true,
				message: useNuxtApp().$translate('FORMS.required'),
				trigger: 'blur'
			}
		],
		email: [
			{
				required: true,
				message: useNuxtApp().$translate('FORMS.required')
			},
			{
				type: 'email',
				message: useNuxtApp().$translate('LOGIN.invalid_email'),
				trigger: 'blur'
			}
		],
		phone: [
			{
				required: true,
				message: useNuxtApp().$translate('FORMS.required')
			}
		]
	});

	const { validate, validateInfos, resetFields } = useForm(requestForm, rulesRef);

	async function onSubmit() {
		try {
			await validate();
			emit('submit', requestForm);
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error(error);
		}
	}

	function closeModal() {
		emit('update:visible', false);
	}

	watch(
		() => props.visible,
		(value) => {
			if (!value) {
				resetFields();
			}
		}
	);
</script>

<style scoped lang="postcss">
	:deep(.ant-form-item-label) {
		padding-bottom: 6px !important;
	}

	:deep(.ant-form-item) {
		margin-bottom: 16px !important;
	}

	:deep(.ant-form-item-label > label) {
		color: var(--grey-700);
		font-family: Inter;
		font-size: 14px;
		font-style: normal;
		font-weight: 500;
		line-height: 20px;
		&::before {
			content: unset !important;
		}
	}

	:deep(.button-wrapper) {
		width: 108px !important;
		height: 44px !important;
	}

	:deep(.ant-select-selector) {
		height: 40px !important;
	}
</style>
