interface AIChainComponent {
	ID: number;
	'Nome Store': string;
	'Nome Store (EN)': string;
	Descrição: string;
	chain: string;
	schema: string;
	'Data Criação (Custom Components)': string;
	'Data Últ. Alteração (Custom Components)': string;
	'Usu<PERSON><PERSON> (Custom Components)': string;
	'Usuário Últ. Alteração (Custom Components)': string;
	'Foto Criação (Custom Components)': string;
	'Foto Últ. Alteração (Custom Components)': string;
	ComponentStore: any | null;
	others: any | null;
	teste: any | null;
}

interface AIChainComponentRefs {
	name_ptbr: string;
	name_en: string;
	id: number;
	key: string;
}
