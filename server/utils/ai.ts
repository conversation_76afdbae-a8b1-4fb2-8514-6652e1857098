/* eslint-disable no-console */
import axios from 'axios';

function getFirstGeminiKey(previousKey?: string): string {
	const { ROTATION_GEMINI_KEYS } = useRuntimeConfig();
	if (!ROTATION_GEMINI_KEYS) {
		throw new Error('ROTATION_GEMINI_KEYS is not defined in runtime config');
	}

	const keysArray = ROTATION_GEMINI_KEYS.split(';')
		.map((key) => key.trim())
		.filter((key) => key);
	if (keysArray.length === 0) {
		throw new Error('No valid keys found in ROTATION_GEMINI_KEYS');
	}

	if (!previousKey) {
		return keysArray[0];
	}

	// Se a previousKey for informada, tenta encontrar a próxima chave diferente
	const nextKey = keysArray.find((key) => maskKey(key) !== previousKey);

	// Se encontrou uma chave diferente, retorna. Senão, retorna a primeira como fallback.
	return nextKey || keysArray[0];
}

export function maskKey(key: string | undefined) {
	if (!key) return '***';
	const start = key.slice(0, 6);
	const end = key.slice(-3);
	return `${start}…${end}`;
}

function getRandomGeminiKey(previousKey?: string): string {
	const { ROTATION_GEMINI_KEYS } = useRuntimeConfig();
	if (!ROTATION_GEMINI_KEYS) {
		throw new Error('ROTATION_GEMINI_KEYS is not defined in runtime config');
	}

	let keysArray = ROTATION_GEMINI_KEYS.split(';')
		.map((key) => key.trim())
		.filter((key) => key);
	if (keysArray.length === 0) {
		throw new Error('No valid keys found in ROTATION_GEMINI_KEYS');
	}

	// Se uma chave anterior foi fornecida, filtra para não usá-la
	if (previousKey) {
		const filteredKeys = keysArray.filter((key) => maskKey(key) !== previousKey);
		// Se ainda houver chaves após a filtragem, usa o array filtrado
		if (filteredKeys.length > 0) {
			keysArray = filteredKeys;
		}
	}

	const randomIndex = Math.floor(Math.random() * keysArray.length);
	return keysArray[randomIndex];
}

/**
 * Obtém a chave de API do Gemini com base nas configurações do usuário e do workspace.
 * A lógica varia se o cliente é Sankhya ou não, e considera créditos gratuitos,
 * uso de créditos do usuário ou chaves do workspace.
 *
 * @param requestUrl URL base da API do Mitra.
 * @param userToken Token de autorização do usuário.
 * @param isToUseUserIaCredit Flag para usar o crédito de IA do usuário.
 * @param isToUseOwnKey Flag para usar a chave própria do usuário.
 * @param workspaceId ID do workspace.
 * @param freeIaCreditsUsage Contagem de uso de créditos gratuitos de IA.
 * @param isSankhya Flag que indica se é um cliente Sankhya.
 * @returns A chave de API do Gemini.
 */
export async function getGeminiApiKey(
	requestUrl: string,
	userToken: string,
	isToUseUserIaCredit: boolean,
	isToUseOwnKey: boolean,
	workspaceId: number,
	freeIaCreditsUsage: number,
	isSankhya: boolean,
	retryAttempt?: number,
	previousKey?: string
): Promise<string> {
	const mitraEcpAxios = axios.create({
		baseURL: requestUrl,
		headers: {
			Accept: 'application/json',
			Authorization: userToken
		}
	});

	try {
		try {
			const userIaConfigResponse = await mitraEcpAxios.get(
				'/mitraspace/user/iaConfig'
			);
			const data = userIaConfigResponse.data;

			// =========================
			// CASO NÃO SEJA SANKHYA
			// =========================
			if (!isSankhya) {
				const isRetry = (retryAttempt ?? 0) > 0;
				// eslint-disable-next-line no-constant-condition
				const MITRA_KEY = true
					? getRandomGeminiKey(previousKey)
					: getFirstGeminiKey(previousKey);
				// ✅ Se ainda há créditos Super IA gratuitos
				if (freeIaCreditsUsage > 0) {
					console.log('🚀 Usando chave do Mitra (créditos gratuitos).');
					if (isRetry) {
						console.log(
							`🔑 Tentativa ${retryAttempt}: Usando chave Gemini rotacionada (aleatória):`,
							maskKey(MITRA_KEY)
						);
					} else {
						console.log(
							'🔑 Primeira tentativa: Usando a primeira chave Gemini:',
							maskKey(MITRA_KEY)
						);
					}
					return MITRA_KEY;
				}

				if (isRetry) {
					console.log(
						`⚠️ Tentativa ${retryAttempt}: Créditos gratuitos esgotados. Tentando chave Gemini rotacionada (aleatória):`,
						maskKey(MITRA_KEY)
					);
					return MITRA_KEY;
				}

				// ✅ Se deve usar os créditos do usuário
				if (isToUseUserIaCredit || isToUseOwnKey) {
					const model = data.model?.find((m: any) => m.id === 11);

					if (model?.modelKey) {
						console.log('✅ Usando a chave de IA do próprio usuário.');
						console.log('🔑 Chave do usuário:', maskKey(model.modelKey));
						return model.modelKey;
					} else {
						throw new Error('❌ modelKey com ID 11 não encontrada.');
					}
				} else {
					// ✅ Se não usa créditos do usuário, consulta por workspace
					const workspaceResponse = await mitraEcpAxios.get(
						`/mitraspace/iaConfig/${workspaceId}/11`
					);

					const modelKey = workspaceResponse.data?.openaiAccessKey;

					if (modelKey) {
						console.log('✅ Usando a chave de IA do workspace.');
						console.log('🔑 Chave do workspace:', maskKey(modelKey));
						return modelKey;
					} else {
						throw new Error('❌ openaiAccessKey do workspace não encontrada.');
					}
				}
			}

			// =========================
			// CASO SEJA SANKHYA → novas regras
			// =========================
			console.log('🟦 Fluxo SANKHYA habilitado');

			// 1) Se isToUseUserIaCredit || isToUseOwnKey: tenta userKey → senão usa callDefaultSankhyaIAKey → senão workspace
			if (isToUseUserIaCredit || isToUseOwnKey) {
				const model = data.model?.find((m: any) => m.id === 11);
				if (model?.modelKey) {
					console.log('✅ [Sankhya] Usando a chave de IA do próprio usuário.');
					console.log('🔑 Chave do usuário (Sankhya):', maskKey(model.modelKey));
					return model.modelKey;
				}

				console.log(
					'⚠️ [Sankhya] modelKey do usuário/own não encontrada. Verificando via workspace...'
				);

				const workspaceResponse = await mitraEcpAxios.get(
					`/mitraspace/iaConfig/${workspaceId}/11`
				);
				const modelKey = workspaceResponse.data?.openaiAccessKey;
				if (modelKey) {
					console.log('✅ [Sankhya] Usando a chave de IA do workspace (fallback).');
					console.log('🔑 Chave do workspace (Sankhya):', maskKey(modelKey));
					return modelKey;
				}
				throw new Error('❌ [Sankhya] openaiAccessKey do workspace não encontrada.');
			}

			// 2) Se NÃO usar user/own key: usa callDefaultSankhyaIAKey → senão workspace
			console.log('ℹ️ [Sankhya] não usando crédito do usuário/own key.');
			{
				const workspaceResponse = await mitraEcpAxios.get(
					`/mitraspace/iaConfig/${workspaceId}/11`
				);
				const modelKey = workspaceResponse.data?.openaiAccessKey;
				if (modelKey) {
					console.log('✅ [Sankhya] Usando a chave de IA do workspace.');
					console.log('🔑 Chave do workspace (Sankhya):', maskKey(modelKey));
					return modelKey;
				}
				throw new Error('❌ [Sankhya] openaiAccessKey do workspace não encontrada.');
			}
		} catch (error) {
			console.error('❌ Erro ao buscar modelKey:', error);
			throw error;
		}
	} catch (error) {
		console.error('❌ Erro ao buscar modelKey:', error);
		throw error;
	}
}
