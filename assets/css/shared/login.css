.ant-input-affix-wrapper,
.ant-input {
	max-height: 40px !important;
}

#login_form_email {
	font-family: Inter;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;
}

#login_form_email::placeholder {
	color: var(--grey-500) !important;
}

.sso-buttons-container {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

@media (max-height: 650px) {
	.sso-buttons-container {
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}

	.sso-button-container {
		width: 100%;
		padding-top: 0.5rem;
	}
}

:deep(.ant-form-item-label label) {
	line-height: 18px !important;
}

:deep(.ant-form-item-label) {
	height: 20px;
	padding-bottom: 0;
	margin-bottom: 6px;
}

:deep(.button-wrapper.simple:active) {
	background-color: var(--violet-700) !important;
}

.ant-form-item {
	/* margin-bottom: 20px; */
}

:deep(.ant-input-affix-wrapper input::placeholder) {
	color: var(--grey-500) !important;
}

.login-button :deep(span) {
	font-size: 16px !important;
}

.auth {
	&-card {
		@apply flex flex-col items-center rounded-xl bg-white px-10 py-8;
		width: 440px;
		@media (max-width: 640px) {
			@apply light-border-all;
		}
		&--nobanner {
			@apply light-border-all;
		}
		&-container {
			@apply flex h-full justify-center p-4 bg-white;
			@media (max-width: 1080px) {
				@apply block overflow-auto;
			}
		}
		&-header {
			@apply flex flex-col items-center w-full medium-border-b pb-5 relative;
			> .divider-text {
				@apply text-grey-700 text-xs bg-white absolute px-2 -bottom-[8px];
			}
		}
		&-left {
			@apply flex-1 flex-col flex justify-center items-center;
			@media (max-width: 640px) {
				@apply !h-full;
			}
		}
	}
	&-form {
		@media theme(screens.smHeight.raw) {
			@apply max-h-[360px] overflow-y-auto;
		}
	}
}

.banner-container {
	border-radius: 12px;
	background-color: var(--grey-400);
	box-shadow: 0 4px 8px -2px rgba(14, 20, 40, 0.1),
		0 2px 4px -2px rgba(14, 20, 40, 0.06);
	background-image: url('~/assets/images/backgrounds/universe_banner.png');
	position: relative;
}

:deep(.container-pwd-by) {
	@apply mt-6;
	@media theme(screens.smHeight.raw) {
		@apply -mt-4;
	}
}

.login-banner {
	@apply flex-1 banner-container bg-cover bg-center items-center flex justify-center noselect;
	@media (max-width: 640px) {
		@apply hidden;
	}
}

:deep(.container-pwd-by.forgot-screen-pwd-by) {
	@media theme(screens.smHeight.raw) {
		@apply mt-6;
	}
}
