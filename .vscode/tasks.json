{"version": "2.0.0", "tasks": [{"label": "Task tirando de preview e apontando na branch validação por padrao", "type": "shell", "command": "git checkout preview; git pull; git checkout -b preview-$(Get-Date -Format yyyyMMdd-HHmm)-${input:suffix}; npm run ${input:npmScript}", "problemMatcher": [], "group": "build", "options": {"shell": {"executable": "powershell.exe", "args": ["-Command"]}}}, {"label": "Criar mcvalida<PERSON><PERSON> branch", "type": "shell", "command": "git rev-parse --abbrev-ref HEAD | ForEach-Object { git checkout -b mc$_ }; git pull origin validacao; npm run dev:validacao", "problemMatcher": [], "group": "build", "options": {"shell": {"executable": "powershell.exe", "args": ["-Command"]}}}], "inputs": [{"id": "suffix", "type": "promptString", "description": "Digite um sufixo opcional para a branch (ex: -fix-chat). De<PERSON>e vazio se não quiser.", "default": ""}, {"id": "npmScript", "type": "promptString", "description": "Qual script npm você quer rodar? (ex: dev:validacao, dev:teste, build)", "default": "dev:<PERSON><PERSON><PERSON>"}]}