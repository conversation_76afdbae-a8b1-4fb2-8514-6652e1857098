export default defineNuxtRouteMiddleware((to) => {
	const AFFILIATE_CODE_COOKIE_NAME = 'mitra_affiliate_code';
	const affiliateCodeCookie = useCookie(AFFILIATE_CODE_COOKIE_NAME, {
		maxAge: 60 * 60 * 2 // 2 horas em segundos
	});
	const prefix = '/link/';

	if (to.path.startsWith(prefix)) {
		const affiliateCode = to.path.substring(prefix.length);

		if (affiliateCode) {
			affiliateCodeCookie.value = affiliateCode;
		}

		const promptId = to.query.promptId as string;
		if (promptId) {
			useAppStore().setPromptId(promptId);
		}

		return navigateTo('/');
	}
});
